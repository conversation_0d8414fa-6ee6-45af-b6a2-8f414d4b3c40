#!/usr/bin/env node

/**
 * Analysis script for crawled data
 */

const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');

class DataAnalyzer {
  constructor() {
    this.outputDir = path.join(process.cwd(), 'output');
  }

  async run() {
    const args = process.argv.slice(2);
    const command = args[0];

    switch (command) {
      case 'latest':
        await this.analyzeLatest();
        break;
      case 'file':
        await this.analyzeFile(args[1]);
        break;
      case 'compare':
        await this.compareFiles(args[1], args[2]);
        break;
      case 'export':
        await this.exportData(args[1], args[2]);
        break;
      case 'stats':
        await this.showStats();
        break;
      default:
        this.showHelp();
        break;
    }
  }

  async analyzeLatest() {
    console.log(chalk.blue.bold('\n📊 Analyzing Latest Crawl Data\n'));

    const files = await this.getOutputFiles();
    if (files.length === 0) {
      console.log(chalk.yellow('No output files found.'));
      return;
    }

    const latestFile = files[0]; // Files are sorted by date
    await this.analyzeFile(latestFile);
  }

  async analyzeFile(filename) {
    if (!filename) {
      console.error(chalk.red('❌ Please specify a filename'));
      return;
    }

    const filePath = path.join(this.outputDir, filename);
    
    if (!await fs.pathExists(filePath)) {
      console.error(chalk.red(`❌ File not found: ${filename}`));
      return;
    }

    console.log(chalk.blue.bold(`\n📊 Analyzing: ${filename}\n`));

    try {
      const data = await fs.readJson(filePath);
      
      // Basic statistics
      console.log(chalk.green('📈 Basic Statistics:'));
      console.log(`Total Properties: ${data.metadata.totalProperties}`);
      console.log(`Extraction Date: ${new Date(data.metadata.extractedAt).toLocaleString()}`);
      console.log(`Sources: ${data.metadata.sites.join(', ')}`);

      if (data.properties && data.properties.length > 0) {
        // Price analysis
        await this.analyzePrices(data.properties);
        
        // Location analysis
        await this.analyzeLocations(data.properties);
        
        // Area analysis
        await this.analyzeAreas(data.properties);
        
        // Source analysis
        await this.analyzeSources(data.properties);
      }

    } catch (error) {
      console.error(chalk.red('❌ Error analyzing file:'), error.message);
    }
  }

  async analyzePrices(properties) {
    console.log(chalk.green('\n💰 Price Analysis:'));
    
    const pricesWithNumbers = properties
      .map(p => p.price)
      .filter(price => price && typeof price === 'string')
      .map(price => {
        const numbers = price.match(/[\d,]+/g);
        return numbers ? parseInt(numbers[0].replace(/,/g, '')) : null;
      })
      .filter(price => price && price > 0);

    if (pricesWithNumbers.length > 0) {
      const min = Math.min(...pricesWithNumbers);
      const max = Math.max(...pricesWithNumbers);
      const avg = Math.round(pricesWithNumbers.reduce((a, b) => a + b, 0) / pricesWithNumbers.length);
      
      console.log(`Price Range: ${min.toLocaleString()} - ${max.toLocaleString()}`);
      console.log(`Average Price: ${avg.toLocaleString()}`);
      console.log(`Properties with Price: ${pricesWithNumbers.length}/${properties.length}`);
    } else {
      console.log('No numeric price data found');
    }
  }

  async analyzeLocations(properties) {
    console.log(chalk.green('\n📍 Location Analysis:'));
    
    const locations = {};
    properties.forEach(p => {
      if (p.location && p.location.trim()) {
        const location = p.location.trim();
        locations[location] = (locations[location] || 0) + 1;
      }
    });

    const sortedLocations = Object.entries(locations)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 10);

    if (sortedLocations.length > 0) {
      console.log('Top 10 Locations:');
      sortedLocations.forEach(([location, count]) => {
        console.log(`  ${location}: ${count} properties`);
      });
    } else {
      console.log('No location data found');
    }
  }

  async analyzeAreas(properties) {
    console.log(chalk.green('\n📐 Area Analysis:'));
    
    const areasWithNumbers = properties
      .map(p => p.area)
      .filter(area => area && typeof area === 'string')
      .map(area => {
        const numbers = area.match(/\d+/);
        return numbers ? parseInt(numbers[0]) : null;
      })
      .filter(area => area && area > 0);

    if (areasWithNumbers.length > 0) {
      const min = Math.min(...areasWithNumbers);
      const max = Math.max(...areasWithNumbers);
      const avg = Math.round(areasWithNumbers.reduce((a, b) => a + b, 0) / areasWithNumbers.length);
      
      console.log(`Area Range: ${min}m² - ${max}m²`);
      console.log(`Average Area: ${avg}m²`);
      console.log(`Properties with Area: ${areasWithNumbers.length}/${properties.length}`);
    } else {
      console.log('No area data found');
    }
  }

  async analyzeSources(properties) {
    console.log(chalk.green('\n🌐 Source Analysis:'));
    
    const sources = {};
    properties.forEach(p => {
      if (p.source) {
        sources[p.source] = (sources[p.source] || 0) + 1;
      }
    });

    Object.entries(sources).forEach(([source, count]) => {
      console.log(`${source}: ${count} properties`);
    });
  }

  async compareFiles(file1, file2) {
    if (!file1 || !file2) {
      console.error(chalk.red('❌ Please specify two filenames to compare'));
      return;
    }

    console.log(chalk.blue.bold(`\n🔄 Comparing: ${file1} vs ${file2}\n`));

    try {
      const data1 = await fs.readJson(path.join(this.outputDir, file1));
      const data2 = await fs.readJson(path.join(this.outputDir, file2));

      console.log(chalk.green('📊 Comparison:'));
      console.log(`File 1 Properties: ${data1.metadata.totalProperties}`);
      console.log(`File 2 Properties: ${data2.metadata.totalProperties}`);
      console.log(`Difference: ${data2.metadata.totalProperties - data1.metadata.totalProperties}`);

      // Compare extraction dates
      const date1 = new Date(data1.metadata.extractedAt);
      const date2 = new Date(data2.metadata.extractedAt);
      console.log(`Time Difference: ${Math.round((date2 - date1) / (1000 * 60))} minutes`);

    } catch (error) {
      console.error(chalk.red('❌ Error comparing files:'), error.message);
    }
  }

  async exportData(filename, format = 'csv') {
    if (!filename) {
      console.error(chalk.red('❌ Please specify a filename'));
      return;
    }

    const filePath = path.join(this.outputDir, filename);
    
    if (!await fs.pathExists(filePath)) {
      console.error(chalk.red(`❌ File not found: ${filename}`));
      return;
    }

    console.log(chalk.blue.bold(`\n📤 Exporting to ${format.toUpperCase()}\n`));

    try {
      const data = await fs.readJson(filePath);
      
      if (format === 'csv') {
        await this.exportToCsv(data, filename);
      } else if (format === 'excel') {
        await this.exportToExcel(data, filename);
      } else {
        console.error(chalk.red('❌ Unsupported format. Use: csv, excel'));
      }

    } catch (error) {
      console.error(chalk.red('❌ Error exporting data:'), error.message);
    }
  }

  async exportToCsv(data, originalFilename) {
    if (!data.properties || data.properties.length === 0) {
      console.log(chalk.yellow('No properties to export'));
      return;
    }

    const headers = ['id', 'title', 'price', 'location', 'area', 'description', 'source', 'url', 'extractedAt'];
    const csvContent = [
      headers.join(','),
      ...data.properties.map(prop => 
        headers.map(header => {
          const value = prop[header] || '';
          return `"${String(value).replace(/"/g, '""')}"`;
        }).join(',')
      )
    ].join('\n');

    const csvFilename = originalFilename.replace('.json', '.csv');
    const csvPath = path.join(this.outputDir, csvFilename);
    
    await fs.writeFile(csvPath, csvContent);
    console.log(chalk.green(`✅ Exported to: ${csvFilename}`));
  }

  async showStats() {
    console.log(chalk.blue.bold('\n📊 Output Directory Statistics\n'));

    const files = await this.getOutputFiles();
    
    if (files.length === 0) {
      console.log(chalk.yellow('No output files found.'));
      return;
    }

    console.log(chalk.green(`Total Files: ${files.length}`));
    
    let totalProperties = 0;
    for (const file of files) {
      try {
        const data = await fs.readJson(path.join(this.outputDir, file));
        totalProperties += data.metadata.totalProperties || 0;
      } catch (error) {
        // Skip corrupted files
      }
    }
    
    console.log(chalk.green(`Total Properties: ${totalProperties}`));
    console.log(chalk.green(`Latest File: ${files[0]}`));
    console.log(chalk.green(`Oldest File: ${files[files.length - 1]}`));
  }

  async getOutputFiles() {
    try {
      const files = await fs.readdir(this.outputDir);
      return files
        .filter(file => file.endsWith('.json'))
        .sort((a, b) => b.localeCompare(a)); // Sort by date (newest first)
    } catch (error) {
      return [];
    }
  }

  showHelp() {
    console.log(chalk.blue.bold('\n📊 Data Analyzer - Help\n'));
    
    console.log(chalk.green('Commands:'));
    console.log('  latest              Analyze the latest crawl data');
    console.log('  file <filename>     Analyze a specific file');
    console.log('  compare <f1> <f2>   Compare two files');
    console.log('  export <file> <fmt> Export data (csv, excel)');
    console.log('  stats               Show output directory statistics');
    
    console.log(chalk.green('\nExamples:'));
    console.log('  node scripts/analyze.js latest');
    console.log('  node scripts/analyze.js file rental_properties_2024-01-15.json');
    console.log('  node scripts/analyze.js export latest.json csv');
    console.log('  node scripts/analyze.js stats');
  }
}

// Run analyzer if this file is executed directly
if (require.main === module) {
  const analyzer = new DataAnalyzer();
  analyzer.run().catch(error => {
    console.error(chalk.red('Analyzer Error:'), error);
    process.exit(1);
  });
}

module.exports = DataAnalyzer;
