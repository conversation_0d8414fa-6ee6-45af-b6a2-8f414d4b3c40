#!/usr/bin/env node

/**
 * Setup script for the Rental Property Crawler
 */

const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');

class Setup {
  constructor() {
    this.projectRoot = process.cwd();
  }

  async run() {
    console.log(chalk.blue.bold('\n🏠 Rental Property Crawler - Setup\n'));

    try {
      await this.createDirectories();
      await this.createConfigFiles();
      await this.createSampleFiles();
      await this.showNextSteps();
    } catch (error) {
      console.error(chalk.red('❌ Setup failed:'), error);
      process.exit(1);
    }
  }

  async createDirectories() {
    console.log(chalk.yellow('📁 Creating directories...'));

    const directories = [
      'output',
      'sessions',
      'logs',
      'data'
    ];

    for (const dir of directories) {
      const dirPath = path.join(this.projectRoot, dir);
      await fs.ensureDir(dirPath);
      console.log(chalk.green(`✅ Created: ${dir}/`));
    }
  }

  async createConfigFiles() {
    console.log(chalk.yellow('\n⚙️ Creating configuration files...'));

    // Create proxy config if it doesn't exist
    const proxyConfigPath = path.join(this.projectRoot, 'proxy.config.js');
    if (!await fs.pathExists(proxyConfigPath)) {
      const examplePath = path.join(this.projectRoot, 'proxy.config.example.js');
      await fs.copy(examplePath, proxyConfigPath);
      console.log(chalk.green('✅ Created: proxy.config.js'));
    } else {
      console.log(chalk.gray('⏭️ Skipped: proxy.config.js (already exists)'));
    }

    // Create captcha config if it doesn't exist
    const captchaConfigPath = path.join(this.projectRoot, 'captcha.config.js');
    if (!await fs.pathExists(captchaConfigPath)) {
      const examplePath = path.join(this.projectRoot, 'captcha.config.example.js');
      await fs.copy(examplePath, captchaConfigPath);
      console.log(chalk.green('✅ Created: captcha.config.js'));
    } else {
      console.log(chalk.gray('⏭️ Skipped: captcha.config.js (already exists)'));
    }

    // Create .gitignore if it doesn't exist
    const gitignorePath = path.join(this.projectRoot, '.gitignore');
    if (!await fs.pathExists(gitignorePath)) {
      const gitignoreContent = `# Dependencies
node_modules/

# Output
output/
sessions/
logs/

# Configuration (contains sensitive data)
proxy.config.js
captcha.config.js

# Environment variables
.env
.env.local

# OS generated files
.DS_Store
Thumbs.db

# IDE files
.vscode/
.idea/

# Temporary files
*.tmp
*.temp
`;
      await fs.writeFile(gitignorePath, gitignoreContent);
      console.log(chalk.green('✅ Created: .gitignore'));
    } else {
      console.log(chalk.gray('⏭️ Skipped: .gitignore (already exists)'));
    }
  }

  async createSampleFiles() {
    console.log(chalk.yellow('\n📄 Creating sample files...'));

    // Create sample proxy list
    const proxyListPath = path.join(this.projectRoot, 'data', 'proxies.example.txt');
    const proxyListContent = `# Proxy list format: host:port:username:password
# Example proxies (replace with real ones):
proxy1.example.com:8080:user1:pass1
proxy2.example.com:8080:user2:pass2
proxy3.example.com:8080:user3:pass3
`;
    await fs.writeFile(proxyListPath, proxyListContent);
    console.log(chalk.green('✅ Created: data/proxies.example.txt'));

    // Create sample URLs for different sites
    const urlsExamplePath = path.join(this.projectRoot, 'urls.example.json');
    const urlsExampleContent = {
      "site": [
        {
          "name": "Demo",
          "url": "https://quotes.toscrape.com/"
        },
        {
          "name": "Chotot",
          "url": "https://www.nhatot.com/thue-bat-dong-san-quan-7-tp-ho-chi-minh?price=5000000-8000000"
        },
        {
          "name": "Chotot-District1",
          "url": "https://www.nhatot.com/thue-bat-dong-san-quan-1-tp-ho-chi-minh"
        },
        {
          "name": "Chotot-Apartments",
          "url": "https://www.nhatot.com/thue-can-ho-chung-cu-tp-ho-chi-minh"
        }
      ]
    };
    await fs.writeJson(urlsExamplePath, urlsExampleContent, { spaces: 2 });
    console.log(chalk.green('✅ Created: urls.example.json'));

    // Create environment template
    const envExamplePath = path.join(this.projectRoot, '.env.example');
    const envExampleContent = `# Rental Property Crawler Environment Variables

# 2Captcha API Key
TWOCAPTCHA_API_KEY=your_2captcha_api_key_here

# Anti-Captcha API Key
ANTICAPTCHA_API_KEY=your_anticaptcha_api_key_here

# Proxy settings
PROXY_HOST=your_proxy_host
PROXY_PORT=8080
PROXY_USERNAME=your_username
PROXY_PASSWORD=your_password

# Browser settings
HEADLESS=true
BROWSER_TIMEOUT=30000

# Crawling settings
MAX_PAGES=10
DELAY_MIN=2000
DELAY_MAX=5000
`;
    await fs.writeFile(envExamplePath, envExampleContent);
    console.log(chalk.green('✅ Created: .env.example'));
  }

  async showNextSteps() {
    console.log(chalk.blue.bold('\n🎉 Setup completed successfully!\n'));

    console.log(chalk.green('Next steps:'));
    console.log('1. Configure your proxy settings in proxy.config.js');
    console.log('2. Configure CAPTCHA solving in captcha.config.js');
    console.log('3. Update urls.json with your target URLs');
    console.log('4. Test your configuration: npm run test-config');
    console.log('5. Start crawling: npm start');

    console.log(chalk.yellow('\nOptional:'));
    console.log('• Copy .env.example to .env and configure environment variables');
    console.log('• Add your proxy list to data/proxies.txt');
    console.log('• Review and customize src/config.js');

    console.log(chalk.blue('\nDocumentation:'));
    console.log('• README.md - General usage and configuration');
    console.log('• CHOTOT_GUIDE.md - Specific guide for Chotot crawling');

    console.log(chalk.gray('\nFor help: npm run crawl help'));
  }
}

// Run setup if this file is executed directly
if (require.main === module) {
  const setup = new Setup();
  setup.run().catch(error => {
    console.error(chalk.red('Setup Error:'), error);
    process.exit(1);
  });
}

module.exports = Setup;
