#!/usr/bin/env node

/**
 * <PERSON><PERSON>t to open the data viewer with the latest crawled data
 */

const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');

class DataViewer {
  constructor() {
    this.outputDir = path.join(process.cwd(), 'output');
    this.viewerPath = path.join(process.cwd(), 'viewer.html');
  }

  async run() {
    const args = process.argv.slice(2);
    const command = args[0];

    switch (command) {
      case 'latest':
        await this.openLatest();
        break;
      case 'file':
        await this.openFile(args[1]);
        break;
      case 'serve':
        await this.startServer();
        break;
      default:
        await this.openLatest();
        break;
    }
  }

  async openLatest() {
    console.log(chalk.blue.bold('\n📊 Opening Data Viewer with Latest Data\n'));

    const files = await this.getOutputFiles();
    if (files.length === 0) {
      console.log(chalk.yellow('❌ No output files found.'));
      console.log(chalk.gray('Run the crawler first: npm start'));
      return;
    }

    const latestFile = files[0];
    console.log(chalk.green(`📁 Latest file: ${latestFile}`));
    
    await this.openViewer();
  }

  async openFile(filename) {
    if (!filename) {
      console.error(chalk.red('❌ Please specify a filename'));
      return;
    }

    const filePath = path.join(this.outputDir, filename);
    
    if (!await fs.pathExists(filePath)) {
      console.error(chalk.red(`❌ File not found: ${filename}`));
      return;
    }

    console.log(chalk.blue.bold(`\n📊 Opening Data Viewer with: ${filename}\n`));
    await this.openViewer();
  }

  async openViewer() {
    if (!await fs.pathExists(this.viewerPath)) {
      console.error(chalk.red('❌ viewer.html not found in project root'));
      return;
    }

    console.log(chalk.green('🌐 Opening data viewer in your default browser...'));
    console.log(chalk.gray(`📍 File: ${this.viewerPath}`));
    
    // Open the HTML file in the default browser
    const { exec } = require('child_process');
    const platform = process.platform;
    
    let command;
    if (platform === 'darwin') {
      command = `open "${this.viewerPath}"`;
    } else if (platform === 'win32') {
      command = `start "" "${this.viewerPath}"`;
    } else {
      command = `xdg-open "${this.viewerPath}"`;
    }

    exec(command, (error) => {
      if (error) {
        console.error(chalk.red('❌ Failed to open browser:'), error.message);
        console.log(chalk.yellow('💡 Manually open: ' + this.viewerPath));
      } else {
        console.log(chalk.green('✅ Data viewer opened successfully!'));
        console.log(chalk.gray('\n📋 Instructions:'));
        console.log(chalk.gray('1. Click "Choose File" in the browser'));
        console.log(chalk.gray('2. Navigate to the output/ directory'));
        console.log(chalk.gray('3. Select a JSON file to view the data'));
      }
    });
  }

  async startServer() {
    console.log(chalk.blue.bold('\n🌐 Starting Local Server for Data Viewer\n'));

    const http = require('http');
    const url = require('url');
    const mime = require('mime-types');

    const server = http.createServer(async (req, res) => {
      const parsedUrl = url.parse(req.url, true);
      let pathname = parsedUrl.pathname;

      // Default to viewer.html
      if (pathname === '/') {
        pathname = '/viewer.html';
      }

      const filePath = path.join(process.cwd(), pathname.substring(1));

      try {
        if (await fs.pathExists(filePath)) {
          const stat = await fs.stat(filePath);
          
          if (stat.isFile()) {
            const content = await fs.readFile(filePath);
            const mimeType = mime.lookup(filePath) || 'application/octet-stream';
            
            res.writeHead(200, {
              'Content-Type': mimeType,
              'Access-Control-Allow-Origin': '*',
              'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
              'Access-Control-Allow-Headers': 'Content-Type'
            });
            res.end(content);
          } else {
            res.writeHead(404);
            res.end('File not found');
          }
        } else {
          res.writeHead(404);
          res.end('File not found');
        }
      } catch (error) {
        res.writeHead(500);
        res.end('Server error: ' + error.message);
      }
    });

    const port = 3000;
    server.listen(port, () => {
      console.log(chalk.green(`✅ Server running at http://localhost:${port}`));
      console.log(chalk.gray('📊 Data viewer available at: http://localhost:${port}/viewer.html'));
      console.log(chalk.gray('📁 Output files available at: http://localhost:${port}/output/'));
      console.log(chalk.yellow('\n💡 Press Ctrl+C to stop the server'));

      // Try to open browser
      const { exec } = require('child_process');
      const platform = process.platform;
      
      let command;
      if (platform === 'darwin') {
        command = `open "http://localhost:${port}"`;
      } else if (platform === 'win32') {
        command = `start "" "http://localhost:${port}"`;
      } else {
        command = `xdg-open "http://localhost:${port}"`;
      }

      exec(command, (error) => {
        if (error) {
          console.log(chalk.yellow('💡 Manually open: http://localhost:' + port));
        }
      });
    });

    // Handle server shutdown
    process.on('SIGINT', () => {
      console.log(chalk.yellow('\n🛑 Shutting down server...'));
      server.close(() => {
        console.log(chalk.green('✅ Server stopped'));
        process.exit(0);
      });
    });
  }

  async getOutputFiles() {
    try {
      const files = await fs.readdir(this.outputDir);
      return files
        .filter(file => file.endsWith('.json'))
        .sort((a, b) => b.localeCompare(a)); // Sort by date (newest first)
    } catch (error) {
      return [];
    }
  }

  showHelp() {
    console.log(chalk.blue.bold('\n📊 Data Viewer - Help\n'));
    
    console.log(chalk.green('Commands:'));
    console.log('  latest              Open viewer with latest data (default)');
    console.log('  file <filename>     Open viewer for specific file');
    console.log('  serve               Start local server for better file access');
    
    console.log(chalk.green('\nExamples:'));
    console.log('  npm run view');
    console.log('  npm run view latest');
    console.log('  npm run view file rental_properties_2024-01-15.json');
    console.log('  npm run view serve');
  }
}

// Install mime-types if not available
async function ensureMimeTypes() {
  try {
    require('mime-types');
  } catch (error) {
    console.log(chalk.yellow('📦 Installing mime-types package...'));
    const { exec } = require('child_process');
    return new Promise((resolve, reject) => {
      exec('npm install mime-types', (error) => {
        if (error) {
          console.log(chalk.yellow('⚠️ Could not install mime-types, using basic server'));
          // Create a simple mime-types mock
          global.mime = {
            lookup: (filePath) => {
              if (filePath.endsWith('.html')) return 'text/html';
              if (filePath.endsWith('.json')) return 'application/json';
              if (filePath.endsWith('.css')) return 'text/css';
              if (filePath.endsWith('.js')) return 'application/javascript';
              return 'application/octet-stream';
            }
          };
          resolve();
        } else {
          resolve();
        }
      });
    });
  }
}

// Run viewer if this file is executed directly
if (require.main === module) {
  const viewer = new DataViewer();
  
  ensureMimeTypes().then(() => {
    viewer.run().catch(error => {
      console.error(chalk.red('Viewer Error:'), error);
      process.exit(1);
    });
  });
}

module.exports = DataViewer;
