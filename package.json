{"name": "chotot-house-rent", "version": "1.0.0", "main": "index.js", "description": "A web crawler for rental property data from Cloudflare-protected sites", "scripts": {"start": "node src/cli.js start", "crawl": "node src/cli.js crawl", "resume": "node src/cli.js resume", "sessions": "node src/cli.js sessions", "session": "node src/cli.js session", "delete-session": "node src/cli.js delete-session", "test-config": "node src/cli.js test", "setup": "node scripts/setup.js", "analyze": "node scripts/analyze.js", "analyze-latest": "node scripts/analyze.js latest", "stats": "node scripts/analyze.js stats", "view": "node scripts/view-data.js", "view-latest": "node scripts/view-data.js latest", "serve": "node scripts/view-data.js serve", "dev": "node index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["web-scraping", "rental-properties", "cloudflare-bypass", "puppeteer", "crawler"], "author": "", "license": "ISC", "engines": {"node": ">=14.0.0"}, "dependencies": {"chalk": "^4.1.2", "fs-extra": "^11.3.0", "mime-types": "^3.0.1", "puppeteer": "^24.10.1", "puppeteer-extra": "^3.3.6", "puppeteer-extra-plugin-adblocker": "^2.13.6", "puppeteer-extra-plugin-stealth": "^2.11.2"}}