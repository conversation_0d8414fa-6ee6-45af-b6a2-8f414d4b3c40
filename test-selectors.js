/**
 * Test script to inspect page structure and find correct selectors
 */

const StealthBrowser = require('./src/utils/stealth');
const config = require('./src/config');

async function testSelectors() {
  const browser = new StealthBrowser(config);
  
  try {
    console.log('🚀 Launching browser...');
    const page = await browser.launch();
    
    console.log('🌐 Navigating to Chotot...');
    await browser.navigateWithRetry('https://www.nhatot.com/thue-bat-dong-san-quan-7-tp-ho-chi-minh?price=5000000-8000000');
    
    console.log('⏳ Waiting for page to load...');
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    console.log('🔍 Inspecting page structure...');
    
    // Get page title
    const title = await page.title();
    console.log('Page title:', title);
    
    // Try to find property cards with various selectors
    const possibleSelectors = [
      '.AdItem_adItem__gDDQT',
      '.AdItem_adItem__2O6dw', 
      '[data-testid="ad-item"]',
      '.ad-item',
      '.item-card',
      '.AdItem',
      '[class*="AdItem"]',
      '[class*="ad-item"]',
      '[class*="item"]',
      'article',
      '.listing-item',
      '.property-item'
    ];
    
    for (const selector of possibleSelectors) {
      try {
        const elements = await page.$$(selector);
        if (elements.length > 0) {
          console.log(`✅ Found ${elements.length} elements with selector: ${selector}`);
          
          // Get some sample content
          const sampleContent = await page.evaluate((sel) => {
            const element = document.querySelector(sel);
            return element ? {
              innerHTML: element.innerHTML.substring(0, 200) + '...',
              textContent: element.textContent.substring(0, 100) + '...',
              className: element.className
            } : null;
          }, selector);
          
          if (sampleContent) {
            console.log('Sample content:', sampleContent);
          }
          break;
        }
      } catch (error) {
        // Continue to next selector
      }
    }
    
    // Get all elements with class containing "ad" or "item"
    const allElements = await page.evaluate(() => {
      const elements = document.querySelectorAll('[class*="ad"], [class*="item"], [class*="Ad"], [class*="Item"]');
      return Array.from(elements).slice(0, 10).map(el => ({
        tagName: el.tagName,
        className: el.className,
        textContent: el.textContent.substring(0, 50) + '...'
      }));
    });
    
    console.log('Elements with "ad" or "item" in class:', allElements);
    
    console.log('✅ Inspection complete. Check the browser window for visual inspection.');
    console.log('Press Ctrl+C to exit when done inspecting...');
    
    // Keep the browser open for manual inspection
    await new Promise(() => {}); // Wait indefinitely
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await browser.close();
  }
}

testSelectors();
