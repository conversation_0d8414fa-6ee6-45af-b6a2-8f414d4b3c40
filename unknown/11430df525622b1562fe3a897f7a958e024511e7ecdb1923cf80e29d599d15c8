#!/usr/bin/env node

/**
 * Main application entry point for scheduled rental property crawler
 */

const ScheduledCrawler = require('./src/scheduled-crawler');

async function main() {
  console.log('🏠 Rental Property Crawler with Slack Notifications');
  console.log('==================================================');
  
  const crawler = new ScheduledCrawler();
  await crawler.start();
}

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  console.error('❌ Uncaught Exception:', error);
  process.exit(1);
});

// Start the application
main().catch(error => {
  console.error('❌ Application failed to start:', error);
  process.exit(1);
});
