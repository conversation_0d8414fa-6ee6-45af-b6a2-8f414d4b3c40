/**
 * Test the actual crawler with updated selectors
 */

const RentalCrawler = require('./src/crawler');

async function testCrawler() {
  const crawler = new RentalCrawler({
    enableProxy: false,
    enableCaptchaSolving: false
  });
  
  try {
    console.log('🚀 Testing crawler with updated selectors...');
    
    // Override the config to limit to 1 page for testing
    crawler.config.crawling.maxPages = 1;
    
    await crawler.run();
    
    console.log('✅ Crawler test completed successfully!');
    
  } catch (error) {
    console.error('❌ Crawler test failed:', error);
  }
}

testCrawler();
