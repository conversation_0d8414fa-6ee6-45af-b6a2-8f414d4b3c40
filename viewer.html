<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Rental Property Crawler - Data Viewer</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            background: #f5f5f7;
            min-height: 100vh;
            padding: 0;
            margin: 0;
            color: #1d1d1f;
            line-height: 1.47059;
            font-weight: 400;
            letter-spacing: -0.022em;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: transparent;
        }

        .header {
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(0, 0, 0, 0.04);
            padding: 20px 40px;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header h1 {
            font-size: 32px;
            font-weight: 600;
            margin: 0;
            color: #1d1d1f;
            letter-spacing: -0.5px;
        }

        .header p {
            font-size: 17px;
            margin: 4px 0 0 0;
            color: #86868b;
            font-weight: 400;
        }

        .controls {
            padding: 32px 40px;
            background: transparent;
        }

        .file-input-container {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 32px;
        }

        .file-input {
            position: relative;
            display: inline-block;
        }

        .file-input input[type="file"] {
            position: absolute;
            opacity: 0;
            width: 100%;
            height: 100%;
            cursor: pointer;
        }

        .file-input-label {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 12px 24px;
            background: #007aff;
            color: white;
            border-radius: 12px;
            font-size: 17px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            border: none;
            letter-spacing: -0.022em;
        }

        .file-input-label:hover {
            background: #0056cc;
            transform: translateY(-1px);
        }

        .file-input-label:active {
            transform: translateY(0);
        }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
            margin-bottom: 40px;
        }

        .stat-card {
            background: white;
            padding: 24px;
            border-radius: 16px;
            border: 1px solid rgba(0, 0, 0, 0.04);
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.04);
            transition: all 0.2s ease;
        }

        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.08);
        }

        .stat-card h3 {
            color: #86868b;
            font-size: 13px;
            margin-bottom: 8px;
            font-weight: 500;
            letter-spacing: -0.01em;
        }

        .stat-card .value {
            font-size: 28px;
            font-weight: 600;
            color: #1d1d1f;
            letter-spacing: -0.5px;
        }

        .content {
            padding: 0 40px 40px 40px;
        }

        .filters {
            display: flex;
            gap: 20px;
            margin-bottom: 32px;
            flex-wrap: wrap;
            align-items: flex-end;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .filter-group label {
            font-weight: 500;
            color: #1d1d1f;
            font-size: 15px;
            letter-spacing: -0.022em;
        }

        .filter-group input,
        .filter-group select {
            padding: 12px 16px;
            border: 1px solid rgba(0, 0, 0, 0.1);
            border-radius: 12px;
            font-size: 17px;
            background: white;
            transition: all 0.2s ease;
            font-family: inherit;
            letter-spacing: -0.022em;
        }

        .filter-group input:focus,
        .filter-group select:focus {
            outline: none;
            border-color: #007aff;
            box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
        }

        .properties-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
            max-width: 100%;
        }

        /* Ensure exactly 4 columns on larger screens */
        @media (min-width: 1200px) {
            .properties-grid {
                grid-template-columns: repeat(4, 1fr);
                gap: 24px;
            }
        }

        .property-card {
            background: white;
            border: 1px solid rgba(0, 0, 0, 0.04);
            border-radius: 18px;
            padding: 0;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.04);
            transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            position: relative;
            overflow: hidden;
        }

        .property-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 32px rgba(0, 0, 0, 0.08);
            border-color: rgba(0, 0, 0, 0.08);
        }

        .property-card-header {
            padding: 24px 24px 20px 24px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.04);
            position: relative;
        }

        .property-title {
            font-size: 19px;
            font-weight: 600;
            color: #1d1d1f;
            margin-bottom: 16px;
            line-height: 1.21053;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            min-height: 2.4em;
            cursor: pointer;
            transition: color 0.2s ease;
            letter-spacing: -0.022em;
        }

        .property-title:hover {
            color: #007aff;
        }

        .property-meta {
            display: flex;
            align-items: center;
            gap: 12px;
            flex-wrap: wrap;
        }

        .price-tag {
            background: #007aff;
            color: white;
            padding: 6px 12px;
            border-radius: 20px;
            font-weight: 600;
            font-size: 15px;
            letter-spacing: -0.022em;
        }

        .location-tag {
            background: rgba(0, 0, 0, 0.04);
            color: #86868b;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 15px;
            font-weight: 500;
            letter-spacing: -0.022em;
        }

        .source-badge {
            position: absolute;
            bottom: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.04);
            color: #86868b;
            padding: 4px 8px;
            border-radius: 8px;
            font-size: 11px;
            font-weight: 500;
            letter-spacing: -0.01em;
        }

        .property-card-body {
            padding: 20px 24px 60px 24px;
        }

        .description {
            color: #86868b;
            font-size: 15px;
            line-height: 1.47059;
            margin-bottom: 16px;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            min-height: 2.4em;
            letter-spacing: -0.022em;
        }

        .extracted-date {
            position: absolute;
            bottom: 20px;
            left: 24px;
            font-size: 11px;
            color: #86868b;
            font-weight: 500;
            letter-spacing: -0.01em;
        }

        .no-data {
            text-align: center;
            padding: 80px 20px;
            color: #86868b;
        }

        .no-data h3 {
            font-size: 22px;
            font-weight: 600;
            margin-bottom: 8px;
            color: #1d1d1f;
            letter-spacing: -0.022em;
        }

        .no-data p {
            font-size: 17px;
            line-height: 1.47059;
            letter-spacing: -0.022em;
        }

        .loading {
            text-align: center;
            padding: 60px;
            color: #86868b;
        }

        .loading h3 {
            font-size: 19px;
            font-weight: 600;
            color: #1d1d1f;
            letter-spacing: -0.022em;
        }

        .error {
            background: rgba(255, 59, 48, 0.1);
            color: #d70015;
            padding: 16px 20px;
            border-radius: 12px;
            margin-bottom: 24px;
            border: 1px solid rgba(255, 59, 48, 0.2);
            font-size: 15px;
            letter-spacing: -0.022em;
        }

        /* Enhanced animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .property-card {
            animation: fadeInUp 0.6s ease-out;
        }

        .property-card:nth-child(even) {
            animation-delay: 0.1s;
        }

        .property-card:nth-child(3n) {
            animation-delay: 0.2s;
        }

        /* Loading skeleton */
        .skeleton {
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
        }

        @keyframes loading {
            0% {
                background-position: 200% 0;
            }

            100% {
                background-position: -200% 0;
            }
        }

        /* Enhanced mobile responsiveness */
        @media (max-width: 1199px) {
            .properties-grid {
                grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                gap: 20px;
            }
        }

        @media (max-width: 992px) {
            .properties-grid {
                grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
                gap: 18px;
            }
        }

        @media (max-width: 768px) {
            body {
                padding: 0;
            }

            .header {
                padding: 16px 20px;
            }

            .header h1 {
                font-size: 28px;
            }

            .header p {
                font-size: 15px;
            }

            .controls {
                padding: 24px 20px;
            }

            .content {
                padding: 0 20px 32px 20px;
            }

            .properties-grid {
                grid-template-columns: 1fr;
                gap: 16px;
            }

            .property-card-header {
                padding: 20px 20px 16px 20px;
            }

            .property-title {
                font-size: 17px;
            }

            .property-meta {
                flex-direction: column;
                align-items: flex-start;
                gap: 8px;
            }

            .filters {
                flex-direction: column;
                gap: 16px;
            }

            .stats {
                grid-template-columns: repeat(2, 1fr);
                gap: 12px;
            }

            .stat-card {
                padding: 20px;
            }
        }

        @media (max-width: 480px) {
            .header {
                padding: 12px 16px;
            }

            .controls {
                padding: 20px 16px;
            }

            .content {
                padding: 0 16px 24px 16px;
            }

            .property-card-body {
                padding: 16px 20px 50px 20px;
            }

            .extracted-date {
                bottom: 16px;
                left: 20px;
            }

            .source-badge {
                bottom: 16px;
                right: 16px;
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <h1>🏠 Rental Property Data Viewer</h1>
            <p>Visualize and analyze your crawled rental property data</p>
        </div>

        <div class="controls">
            <div class="file-input-container">
                <div class="file-input">
                    <input type="file" id="fileInput" accept=".json" />
                    <label for="fileInput" class="file-input-label">
                        📁 Choose JSON File
                    </label>
                </div>
            </div>

            <div class="stats" id="stats" style="display: none;">
                <div class="stat-card">
                    <h3>Total Properties</h3>
                    <div class="value" id="totalProperties">0</div>
                </div>
                <div class="stat-card">
                    <h3>Sources</h3>
                    <div class="value" id="totalSources">0</div>
                </div>
                <div class="stat-card">
                    <h3>Extracted At</h3>
                    <div class="value" id="extractedAt">-</div>
                </div>
                <div class="stat-card">
                    <h3>Avg Price</h3>
                    <div class="value" id="avgPrice">-</div>
                </div>
            </div>
        </div>

        <div class="content">
            <div class="filters" id="filters" style="display: none;">
                <div class="filter-group">
                    <label>Search</label>
                    <input type="text" id="searchInput" placeholder="Search in title, location..." />
                </div>
                <div class="filter-group">
                    <label>Source</label>
                    <select id="sourceFilter">
                        <option value="">All Sources</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label>Sort By</label>
                    <select id="sortBy">
                        <option value="extractedAt">Date</option>
                        <option value="title">Title</option>
                        <option value="price">Price</option>
                        <option value="location">Location</option>
                    </select>
                </div>
            </div>

            <div id="error" class="error" style="display: none;"></div>
            <div id="loading" class="loading" style="display: none;">
                <h3>📊 Loading data...</h3>
            </div>

            <div id="noData" class="no-data">
                <h3>📁 No Data Loaded</h3>
                <p>Please select a JSON file from your crawled data to view the properties.</p>
                <p style="margin-top: 10px; font-size: 0.9em; color: #888;">
                    Files are typically located in the <code>output/</code> directory.
                </p>
            </div>

            <div id="propertiesContainer" class="properties-grid"></div>
        </div>
    </div>

    <script>
        let allProperties = [];
        let filteredProperties = [];

        document.getElementById('fileInput').addEventListener('change', handleFileSelect);
        document.getElementById('searchInput').addEventListener('input', filterProperties);
        document.getElementById('sourceFilter').addEventListener('change', filterProperties);
        document.getElementById('sortBy').addEventListener('change', filterProperties);

        function handleFileSelect(event) {
            const file = event.target.files[0];
            if (!file) return;

            showLoading();

            const reader = new FileReader();
            reader.onload = function (e) {
                try {
                    const data = JSON.parse(e.target.result);
                    loadData(data);
                } catch (error) {
                    showError('Invalid JSON file. Please select a valid crawled data file.');
                }
            };
            reader.readAsText(file);
        }

        function loadData(data) {
            hideLoading();
            hideError();

            if (!data.properties || !Array.isArray(data.properties)) {
                showError('Invalid data format. Expected properties array.');
                return;
            }

            allProperties = data.properties;
            filteredProperties = [...allProperties];

            updateStats(data);
            updateSourceFilter();
            filterProperties();

            document.getElementById('stats').style.display = 'grid';
            document.getElementById('filters').style.display = 'flex';
            document.getElementById('noData').style.display = 'none';
        }

        function updateStats(data) {
            document.getElementById('totalProperties').textContent = data.metadata.totalProperties || allProperties.length;
            document.getElementById('totalSources').textContent = data.metadata.sites ? data.metadata.sites.length : new Set(allProperties.map(p => p.source)).size;

            if (data.metadata.extractedAt) {
                const date = new Date(data.metadata.extractedAt);
                document.getElementById('extractedAt').textContent = date.toLocaleDateString();
            }

            // Calculate average price
            const prices = allProperties
                .map(p => p.price)
                .filter(price => price && typeof price === 'string')
                .map(price => {
                    const numbers = price.match(/[\d,]+/g);
                    return numbers ? parseInt(numbers[0].replace(/,/g, '')) : null;
                })
                .filter(price => price && price > 0);

            if (prices.length > 0) {
                const avg = Math.round(prices.reduce((a, b) => a + b, 0) / prices.length);
                document.getElementById('avgPrice').textContent = avg.toLocaleString();
            }
        }

        function updateSourceFilter() {
            const sources = [...new Set(allProperties.map(p => p.source))];
            const sourceFilter = document.getElementById('sourceFilter');

            // Clear existing options except "All Sources"
            sourceFilter.innerHTML = '<option value="">All Sources</option>';

            sources.forEach(source => {
                const option = document.createElement('option');
                option.value = source;
                option.textContent = source;
                sourceFilter.appendChild(option);
            });
        }

        function filterProperties() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const sourceFilter = document.getElementById('sourceFilter').value;
            const sortBy = document.getElementById('sortBy').value;

            filteredProperties = allProperties.filter(property => {
                const matchesSearch = !searchTerm ||
                    (property.title && property.title.toLowerCase().includes(searchTerm)) ||
                    (property.location && property.location.toLowerCase().includes(searchTerm)) ||
                    (property.description && property.description.toLowerCase().includes(searchTerm));

                const matchesSource = !sourceFilter || property.source === sourceFilter;

                return matchesSearch && matchesSource;
            });

            // Sort properties
            filteredProperties.sort((a, b) => {
                if (sortBy === 'extractedAt') {
                    return new Date(b.extractedAt) - new Date(a.extractedAt);
                } else if (sortBy === 'title') {
                    return (a.title || '').localeCompare(b.title || '');
                } else if (sortBy === 'price') {
                    const priceA = extractNumber(a.price);
                    const priceB = extractNumber(b.price);
                    return priceB - priceA;
                } else if (sortBy === 'location') {
                    return (a.location || '').localeCompare(b.location || '');
                }
                return 0;
            });

            renderProperties();
        }

        function extractNumber(str) {
            if (!str) return 0;
            const numbers = str.match(/[\d,]+/g);
            return numbers ? parseInt(numbers[0].replace(/,/g, '')) : 0;
        }

        function renderProperties() {
            const container = document.getElementById('propertiesContainer');

            if (filteredProperties.length === 0) {
                container.innerHTML = '<div class="no-data"><h3>🔍 No Properties Found</h3><p>Try adjusting your search or filter criteria.</p></div>';
                return;
            }

            container.innerHTML = filteredProperties.map(property => {
                const cleanPrice = property.price ? property.price.replace(/[^\d,]/g, '') : 'N/A';
                const cleanLocation = property.location ? property.location.split('•')[0].trim() : 'N/A';
                const extractedDate = property.extractedAt ? new Date(property.extractedAt).toLocaleDateString() : 'N/A';

                return `
                <div class="property-card">
                    <div class="property-card-header">
                        <div class="property-title" onclick="window.open('${property.url || '#'}', '_blank')">${property.title || 'No Title Available'}</div>
                        <div class="property-meta">
                            <div class="price-tag">${cleanPrice} triệu</div>
                            <div class="location-tag">${cleanLocation}</div>
                        </div>
                    </div>

                    <div class="property-card-body">
                        <div class="extracted-date">${extractedDate}</div>
                        <div class="source-badge">${property.source || 'unknown'}</div>
                    </div>
                </div>
                
            `;
            }).join('');
        }

        function showLoading() {
            document.getElementById('loading').style.display = 'block';
            document.getElementById('noData').style.display = 'none';
            document.getElementById('error').style.display = 'none';
        }

        function hideLoading() {
            document.getElementById('loading').style.display = 'none';
        }

        function showError(message) {
            document.getElementById('error').textContent = message;
            document.getElementById('error').style.display = 'block';
            document.getElementById('loading').style.display = 'none';
        }

        function hideError() {
            document.getElementById('error').style.display = 'none';
        }
    </script>
</body>

</html>