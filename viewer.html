<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Rental Property Crawler - Data Viewer</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .controls {
            padding: 20px 30px;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
        }

        .file-input-container {
            display: flex;
            align-items: center;
            gap: 15px;
            flex-wrap: wrap;
        }

        .file-input {
            flex: 1;
            min-width: 300px;
        }

        .file-input input[type="file"] {
            width: 100%;
            padding: 12px;
            border: 2px dashed #667eea;
            border-radius: 8px;
            background: white;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .file-input input[type="file"]:hover {
            border-color: #764ba2;
            background: #f8f9ff;
        }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }

        .stat-card {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #667eea;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .stat-card h3 {
            color: #333;
            font-size: 0.9em;
            margin-bottom: 5px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .stat-card .value {
            font-size: 1.8em;
            font-weight: bold;
            color: #667eea;
        }

        .content {
            padding: 30px;
        }

        .filters {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .filter-group label {
            font-weight: 600;
            color: #333;
            font-size: 0.9em;
        }

        .filter-group input, .filter-group select {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 0.9em;
        }

        .properties-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .property-card {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            position: relative;
        }

        .property-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .property-title {
            font-size: 1.1em;
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
            line-height: 1.4;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .property-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-bottom: 15px;
        }

        .detail-item {
            display: flex;
            flex-direction: column;
            gap: 2px;
        }

        .detail-label {
            font-size: 0.8em;
            color: #666;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .detail-value {
            font-weight: 600;
            color: #333;
        }

        .price {
            color: #e74c3c;
            font-size: 1.1em;
        }

        .location {
            color: #3498db;
        }

        .area {
            color: #27ae60;
        }

        .source {
            position: absolute;
            top: 15px;
            right: 15px;
            background: #667eea;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.7em;
            font-weight: 600;
        }

        .description {
            color: #666;
            font-size: 0.9em;
            line-height: 1.4;
            margin-top: 10px;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .no-data {
            text-align: center;
            padding: 60px 20px;
            color: #666;
        }

        .no-data h3 {
            font-size: 1.5em;
            margin-bottom: 10px;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #667eea;
        }

        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #f5c6cb;
        }

        @media (max-width: 768px) {
            .properties-grid {
                grid-template-columns: 1fr;
            }
            
            .file-input-container {
                flex-direction: column;
            }
            
            .filters {
                flex-direction: column;
            }
            
            .stats {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏠 Rental Property Data Viewer</h1>
            <p>Visualize and analyze your crawled rental property data</p>
        </div>

        <div class="controls">
            <div class="file-input-container">
                <div class="file-input">
                    <input type="file" id="fileInput" accept=".json" />
                </div>
            </div>
            
            <div class="stats" id="stats" style="display: none;">
                <div class="stat-card">
                    <h3>Total Properties</h3>
                    <div class="value" id="totalProperties">0</div>
                </div>
                <div class="stat-card">
                    <h3>Sources</h3>
                    <div class="value" id="totalSources">0</div>
                </div>
                <div class="stat-card">
                    <h3>Extracted At</h3>
                    <div class="value" id="extractedAt">-</div>
                </div>
                <div class="stat-card">
                    <h3>Avg Price</h3>
                    <div class="value" id="avgPrice">-</div>
                </div>
            </div>
        </div>

        <div class="content">
            <div class="filters" id="filters" style="display: none;">
                <div class="filter-group">
                    <label>Search</label>
                    <input type="text" id="searchInput" placeholder="Search in title, location..." />
                </div>
                <div class="filter-group">
                    <label>Source</label>
                    <select id="sourceFilter">
                        <option value="">All Sources</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label>Sort By</label>
                    <select id="sortBy">
                        <option value="extractedAt">Date</option>
                        <option value="title">Title</option>
                        <option value="price">Price</option>
                        <option value="location">Location</option>
                    </select>
                </div>
            </div>

            <div id="error" class="error" style="display: none;"></div>
            <div id="loading" class="loading" style="display: none;">
                <h3>📊 Loading data...</h3>
            </div>
            
            <div id="noData" class="no-data">
                <h3>📁 No Data Loaded</h3>
                <p>Please select a JSON file from your crawled data to view the properties.</p>
                <p style="margin-top: 10px; font-size: 0.9em; color: #888;">
                    Files are typically located in the <code>output/</code> directory.
                </p>
            </div>

            <div id="propertiesContainer" class="properties-grid"></div>
        </div>
    </div>

    <script>
        let allProperties = [];
        let filteredProperties = [];

        document.getElementById('fileInput').addEventListener('change', handleFileSelect);
        document.getElementById('searchInput').addEventListener('input', filterProperties);
        document.getElementById('sourceFilter').addEventListener('change', filterProperties);
        document.getElementById('sortBy').addEventListener('change', filterProperties);

        function handleFileSelect(event) {
            const file = event.target.files[0];
            if (!file) return;

            showLoading();
            
            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const data = JSON.parse(e.target.result);
                    loadData(data);
                } catch (error) {
                    showError('Invalid JSON file. Please select a valid crawled data file.');
                }
            };
            reader.readAsText(file);
        }

        function loadData(data) {
            hideLoading();
            hideError();
            
            if (!data.properties || !Array.isArray(data.properties)) {
                showError('Invalid data format. Expected properties array.');
                return;
            }

            allProperties = data.properties;
            filteredProperties = [...allProperties];
            
            updateStats(data);
            updateSourceFilter();
            filterProperties();
            
            document.getElementById('stats').style.display = 'grid';
            document.getElementById('filters').style.display = 'flex';
            document.getElementById('noData').style.display = 'none';
        }

        function updateStats(data) {
            document.getElementById('totalProperties').textContent = data.metadata.totalProperties || allProperties.length;
            document.getElementById('totalSources').textContent = data.metadata.sites ? data.metadata.sites.length : new Set(allProperties.map(p => p.source)).size;
            
            if (data.metadata.extractedAt) {
                const date = new Date(data.metadata.extractedAt);
                document.getElementById('extractedAt').textContent = date.toLocaleDateString();
            }

            // Calculate average price
            const prices = allProperties
                .map(p => p.price)
                .filter(price => price && typeof price === 'string')
                .map(price => {
                    const numbers = price.match(/[\d,]+/g);
                    return numbers ? parseInt(numbers[0].replace(/,/g, '')) : null;
                })
                .filter(price => price && price > 0);

            if (prices.length > 0) {
                const avg = Math.round(prices.reduce((a, b) => a + b, 0) / prices.length);
                document.getElementById('avgPrice').textContent = avg.toLocaleString();
            }
        }

        function updateSourceFilter() {
            const sources = [...new Set(allProperties.map(p => p.source))];
            const sourceFilter = document.getElementById('sourceFilter');
            
            // Clear existing options except "All Sources"
            sourceFilter.innerHTML = '<option value="">All Sources</option>';
            
            sources.forEach(source => {
                const option = document.createElement('option');
                option.value = source;
                option.textContent = source;
                sourceFilter.appendChild(option);
            });
        }

        function filterProperties() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const sourceFilter = document.getElementById('sourceFilter').value;
            const sortBy = document.getElementById('sortBy').value;

            filteredProperties = allProperties.filter(property => {
                const matchesSearch = !searchTerm || 
                    (property.title && property.title.toLowerCase().includes(searchTerm)) ||
                    (property.location && property.location.toLowerCase().includes(searchTerm)) ||
                    (property.description && property.description.toLowerCase().includes(searchTerm));
                
                const matchesSource = !sourceFilter || property.source === sourceFilter;
                
                return matchesSearch && matchesSource;
            });

            // Sort properties
            filteredProperties.sort((a, b) => {
                if (sortBy === 'extractedAt') {
                    return new Date(b.extractedAt) - new Date(a.extractedAt);
                } else if (sortBy === 'title') {
                    return (a.title || '').localeCompare(b.title || '');
                } else if (sortBy === 'price') {
                    const priceA = extractNumber(a.price);
                    const priceB = extractNumber(b.price);
                    return priceB - priceA;
                } else if (sortBy === 'location') {
                    return (a.location || '').localeCompare(b.location || '');
                }
                return 0;
            });

            renderProperties();
        }

        function extractNumber(str) {
            if (!str) return 0;
            const numbers = str.match(/[\d,]+/g);
            return numbers ? parseInt(numbers[0].replace(/,/g, '')) : 0;
        }

        function renderProperties() {
            const container = document.getElementById('propertiesContainer');
            
            if (filteredProperties.length === 0) {
                container.innerHTML = '<div class="no-data"><h3>🔍 No Properties Found</h3><p>Try adjusting your search or filter criteria.</p></div>';
                return;
            }

            container.innerHTML = filteredProperties.map(property => `
                <div class="property-card">
                    <div class="source">${property.source || 'Unknown'}</div>
                    <div class="property-title">${property.title || 'No Title'}</div>
                    <div class="property-details">
                        <div class="detail-item">
                            <div class="detail-label">Price</div>
                            <div class="detail-value price">${property.price || 'N/A'}</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">Area</div>
                            <div class="detail-value area">${property.area || 'N/A'}</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">Location</div>
                            <div class="detail-value location">${property.location || 'N/A'}</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">Extracted</div>
                            <div class="detail-value">${property.extractedAt ? new Date(property.extractedAt).toLocaleDateString() : 'N/A'}</div>
                        </div>
                    </div>
                    ${property.description ? `<div class="description">${property.description}</div>` : ''}
                    ${property.url ? `<div style="margin-top: 10px;"><a href="${property.url}" target="_blank" style="color: #667eea; text-decoration: none; font-size: 0.9em;">🔗 View Original</a></div>` : ''}
                </div>
            `).join('');
        }

        function showLoading() {
            document.getElementById('loading').style.display = 'block';
            document.getElementById('noData').style.display = 'none';
            document.getElementById('error').style.display = 'none';
        }

        function hideLoading() {
            document.getElementById('loading').style.display = 'none';
        }

        function showError(message) {
            document.getElementById('error').textContent = message;
            document.getElementById('error').style.display = 'block';
            document.getElementById('loading').style.display = 'none';
        }

        function hideError() {
            document.getElementById('error').style.display = 'none';
        }
    </script>
</body>
</html>
