/**
 * Proxy configuration example
 * Copy this file to proxy.config.js and update with your proxy settings
 */

module.exports = {
  // Enable/disable proxy usage
  enabled: false,

  // Single proxy configuration
  host: 'your-proxy-host.com',
  port: 8080,
  username: 'your-username',
  password: 'your-password',
  type: 'http', // http, https, socks4, socks5

  // Multiple proxy list (will be rotated)
  list: [
    {
      host: 'proxy1.example.com',
      port: 8080,
      username: 'user1',
      password: 'pass1',
      type: 'http'
    },
    {
      host: 'proxy2.example.com',
      port: 8080,
      username: 'user2',
      password: 'pass2',
      type: 'http'
    }
  ],

  // Load proxies from file (one per line: host:port:username:password)
  file: './proxies.txt',

  // Proxy validation settings
  validation: {
    enabled: true,
    timeout: 10000,
    testUrl: 'https://httpbin.org/ip'
  }
};
