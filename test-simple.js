/**
 * Simple test with a basic page first
 */

const StealthBrowser = require('./src/utils/stealth');
const config = require('./src/config');

async function testSimple() {
  const browser = new StealthBrowser(config);
  
  try {
    console.log('🚀 Launching browser...');
    const page = await browser.launch();
    
    // First try a simple page to test our setup
    console.log('🌐 Testing with Google...');
    await page.goto('https://www.google.com', { waitUntil: 'networkidle2' });
    
    const googleTitle = await page.title();
    console.log('Google title:', googleTitle);
    
    // Now try the main page of nhatot
    console.log('🌐 Navigating to Nhatot main page...');
    await page.goto('https://www.nhatot.com', { waitUntil: 'networkidle2' });
    
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    const nhatotTitle = await page.title();
    console.log('Nhatot title:', nhatotTitle);
    
    if (nhatotTitle.includes('Just a moment')) {
      console.log('❌ Still hitting Cloudflare on main page');
    } else {
      console.log('✅ Successfully accessed Nhatot main page');
      
      // Try to navigate to the rental section
      console.log('🌐 Navigating to rental section...');
      await page.goto('https://www.nhatot.com/thue-bat-dong-san', { waitUntil: 'networkidle2' });
      
      await new Promise(resolve => setTimeout(resolve, 5000));
      
      const rentalTitle = await page.title();
      console.log('Rental page title:', rentalTitle);
      
      if (!rentalTitle.includes('Just a moment')) {
        // Look for any property-like elements
        const elements = await page.evaluate(() => {
          const allElements = Array.from(document.querySelectorAll('*[class]'))
            .filter(el => el.textContent.trim().length > 10 && el.textContent.trim().length < 200)
            .slice(0, 20)
            .map(el => ({
              tagName: el.tagName,
              className: el.className,
              textContent: el.textContent.trim().substring(0, 100)
            }));
          
          return {
            title: document.title,
            elementCount: document.querySelectorAll('*').length,
            sampleElements: allElements
          };
        });
        
        console.log('Page analysis:', elements);
      }
    }
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await browser.close();
  }
}

testSimple();
