/**
 * Main crawler class for rental property data extraction
 */

const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');
const StealthBrowser = require('./utils/stealth');
const ChototExtractor = require('./extractors/chotot');
const DemoExtractor = require('./extractors/demo');
const ProxyManager = require('./utils/proxy');
const CaptchaHandler = require('./utils/captcha');
const SessionManager = require('./utils/session');
const config = require('./config');

class RentalCrawler {
  constructor(options = {}) {
    this.config = config;
    this.browser = new StealthBrowser(config);
    this.proxyManager = new ProxyManager(config);
    this.captchaHandler = new CaptchaHandler(config);
    this.sessionManager = new SessionManager(config);
    this.extractors = {
      chotot: new ChototExtractor(config),
      demo: new DemoExtractor(config)
    };
    this.allProperties = [];
    this.options = {
      resumeSession: options.resumeSession || null,
      createNewSession: options.createNewSession || true,
      enableProxy: options.enableProxy !== false,
      enableCaptchaSolving: options.enableCaptchaSolving || false,
      ...options
    };
  }

  async initialize() {
    try {
      console.log(chalk.blue('🚀 Initializing Rental Property Crawler...'));

      // Ensure output directory exists
      await fs.ensureDir(this.config.output.directory);

      // Initialize session manager
      await this.sessionManager.initialize();

      // Handle session resumption
      if (this.options.resumeSession) {
        const loaded = await this.sessionManager.loadSession(this.options.resumeSession);
        if (!loaded) {
          console.log(chalk.yellow(`⚠️ Could not load session ${this.options.resumeSession}, creating new one`));
          await this.sessionManager.createSession();
        }
      } else if (this.options.createNewSession) {
        await this.sessionManager.createSession();
      }

      // Initialize proxy manager
      if (this.options.enableProxy) {
        await this.proxyManager.initialize();
      }

      // Launch browser with proxy support
      this.page = await this.browser.launch(this.proxyManager);

      // Apply session data to page
      if (this.sessionManager.currentSession) {
        await this.sessionManager.applySessionToPage(this.page);
        this.sessionManager.startAutoSave();
      }

      console.log(chalk.green('✅ Crawler initialized successfully'));
    } catch (error) {
      console.error(chalk.red('❌ Failed to initialize crawler:'), error);
      throw error;
    }
  }

  async loadUrls() {
    try {
      const urlsPath = path.join(process.cwd(), 'urls.json');
      const urlsData = await fs.readJson(urlsPath);

      console.log(chalk.blue(`📋 Loaded ${urlsData.site.length} site(s) to crawl`));
      return urlsData.site;
    } catch (error) {
      console.error(chalk.red('❌ Failed to load URLs:'), error);
      throw error;
    }
  }

  async crawlSite(siteConfig) {
    try {
      console.log(chalk.yellow(`\n🏠 Starting to crawl ${siteConfig.name}...`));
      console.log(chalk.gray(`URL: ${siteConfig.url}`));

      const extractor = this.getExtractor(siteConfig.name);
      if (!extractor) {
        throw new Error(`No extractor found for site: ${siteConfig.name}`);
      }

      // Navigate to the site
      await this.browser.navigateWithRetry(siteConfig.url);

      // Wait for page to load
      await this.browser.randomDelay(2000, 4000);

      let pageCount = 0;
      let totalProperties = 0;
      const maxPages = this.config.crawling.maxPages;

      while (pageCount < maxPages) {
        pageCount++;
        console.log(chalk.blue(`\n📄 Processing page ${pageCount}/${maxPages}...`));

        try {
          // Scroll to load more content if needed
          await extractor.scrollToLoadMore(this.page, this.config.sites.chotot.maxScrolls);

          // Extract property data
          const properties = await extractor.extractPropertyData(this.page);

          if (properties.length === 0) {
            console.log(chalk.yellow('⚠️ No properties found on this page'));
            break;
          }

          // Clean and add properties
          const cleanedProperties = properties.map(prop => extractor.cleanPropertyData(prop));
          this.allProperties.push(...cleanedProperties);
          totalProperties += cleanedProperties.length;

          console.log(chalk.green(`✅ Extracted ${cleanedProperties.length} properties from page ${pageCount}`));

          // Check if there's a next page
          const hasNext = await extractor.hasNextPage(this.page);
          if (!hasNext) {
            console.log(chalk.blue('📄 No more pages available'));
            break;
          }

          // Navigate to next page
          const navigated = await extractor.goToNextPage(this.page);
          if (!navigated) {
            console.log(chalk.yellow('⚠️ Could not navigate to next page'));
            break;
          }

          // Random delay between pages
          await this.browser.randomDelay(
            this.config.crawling.delay.min,
            this.config.crawling.delay.max
          );

        } catch (error) {
          console.error(chalk.red(`❌ Error on page ${pageCount}:`), error.message);

          // Try to continue with next page
          if (pageCount < maxPages) {
            console.log(chalk.yellow('🔄 Attempting to continue...'));
            await this.browser.randomDelay(5000, 8000);
          }
        }
      }

      console.log(chalk.green(`\n✅ Completed crawling ${siteConfig.name}`));
      console.log(chalk.green(`📊 Total properties extracted: ${totalProperties}`));

      return totalProperties;
    } catch (error) {
      console.error(chalk.red(`❌ Failed to crawl ${siteConfig.name}:`), error);
      throw error;
    }
  }

  getExtractor(siteName) {
    const normalizedName = siteName.toLowerCase();

    if (normalizedName.includes('demo')) {
      return this.extractors.demo;
    } else if (normalizedName.includes('chotot') || normalizedName.includes('nhatot')) {
      return this.extractors.chotot;
    }

    return null;
  }

  async saveResults() {
    try {
      if (this.allProperties.length === 0) {
        console.log(chalk.yellow('⚠️ No properties to save'));
        return;
      }

      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const filename = `${this.config.output.filename}_${timestamp}`;

      // Save as JSON
      const jsonPath = path.join(this.config.output.directory, `${filename}.json`);
      await fs.writeJson(jsonPath, {
        metadata: {
          totalProperties: this.allProperties.length,
          extractedAt: new Date().toISOString(),
          sites: [...new Set(this.allProperties.map(p => p.source))]
        },
        properties: this.allProperties
      }, { spaces: 2 });

      // Save as CSV if requested
      if (this.config.output.format === 'csv') {
        const csvPath = path.join(this.config.output.directory, `${filename}.csv`);
        await this.saveAsCSV(csvPath);
      }

      console.log(chalk.green(`\n💾 Results saved:`));
      console.log(chalk.gray(`📁 JSON: ${jsonPath}`));
      console.log(chalk.gray(`📊 Total properties: ${this.allProperties.length}`));

    } catch (error) {
      console.error(chalk.red('❌ Failed to save results:'), error);
      throw error;
    }
  }

  async saveAsCSV(csvPath) {
    // Simple CSV conversion
    if (this.allProperties.length === 0) return;

    const headers = Object.keys(this.allProperties[0]).filter(key => typeof this.allProperties[0][key] !== 'object');
    const csvContent = [
      headers.join(','),
      ...this.allProperties.map(prop =>
        headers.map(header => {
          const value = prop[header] || '';
          return `"${String(value).replace(/"/g, '""')}"`;
        }).join(',')
      )
    ].join('\n');

    await fs.writeFile(csvPath, csvContent);
    console.log(chalk.gray(`📊 CSV: ${csvPath}`));
  }

  async run() {
    try {
      await this.initialize();

      const sites = await this.loadUrls();

      for (const site of sites) {
        await this.crawlSite(site);
      }

      await this.saveResults();

      console.log(chalk.green('\n🎉 Crawling completed successfully!'));

    } catch (error) {
      console.error(chalk.red('\n💥 Crawling failed:'), error);
      throw error;
    } finally {
      await this.cleanup();
    }
  }

  async cleanup() {
    try {
      await this.browser.close();
      console.log(chalk.blue('🧹 Cleanup completed'));
    } catch (error) {
      console.error(chalk.red('❌ Cleanup error:'), error);
    }
  }
}

module.exports = RentalCrawler;
