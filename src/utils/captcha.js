/**
 * CAPTCHA and challenge handling utilities
 */

class CaptchaHandler {
  constructor(config) {
    this.config = config.captcha || {};
    this.solvers = {};

    // Initialize available CAPTCHA solving services
    try {
      if (this.config.twoCaptcha?.apiKey) {
        this.solvers.twoCaptcha = require('./captcha-solvers/two-captcha');
      }
    } catch (error) {
      console.log('2Captcha solver not available');
    }

    try {
      if (this.config.antiCaptcha?.apiKey) {
        this.solvers.antiCaptcha = require('./captcha-solvers/anti-captcha');
      }
    } catch (error) {
      console.log('Anti-Captcha solver not available');
    }
  }

  /**
   * Detect CAPTCHA type on page
   */
  async detectCaptcha(page) {
    try {
      const captchaInfo = await page.evaluate(() => {
        const detections = {
          recaptcha: {
            v2: !!document.querySelector('.g-recaptcha, #recaptcha'),
            v3: !!document.querySelector('[data-sitekey]') || window.grecaptcha,
            invisible: !!document.querySelector('.g-recaptcha[data-size="invisible"]')
          },
          hcaptcha: !!document.querySelector('.h-captcha, [data-hcaptcha-sitekey]'),
          cloudflare: {
            challenge: !!document.querySelector('#challenge-form, .cf-browser-verification'),
            turnstile: !!document.querySelector('[data-cf-turnstile-sitekey]')
          },
          funcaptcha: !!document.querySelector('#funcaptcha, .funcaptcha'),
          geetest: !!document.querySelector('.geetest_holder, #geetest-captcha')
        };

        // Get site keys if available
        const siteKeys = {};
        const recaptchaSiteKey = document.querySelector('[data-sitekey]');
        if (recaptchaSiteKey) {
          siteKeys.recaptcha = recaptchaSiteKey.getAttribute('data-sitekey');
        }

        const hcaptchaSiteKey = document.querySelector('[data-hcaptcha-sitekey]');
        if (hcaptchaSiteKey) {
          siteKeys.hcaptcha = hcaptchaSiteKey.getAttribute('data-hcaptcha-sitekey');
        }

        const cloudflareSiteKey = document.querySelector('[data-cf-turnstile-sitekey]');
        if (cloudflareSiteKey) {
          siteKeys.cloudflare = cloudflareSiteKey.getAttribute('data-cf-turnstile-sitekey');
        }

        return { detections, siteKeys };
      });

      return captchaInfo;
    } catch (error) {
      console.error('Error detecting CAPTCHA:', error);
      return { detections: {}, siteKeys: {} };
    }
  }

  /**
   * Handle Cloudflare challenge
   */
  async handleCloudflareChallenge(page) {
    try {
      console.log('🛡️ Handling Cloudflare challenge...');

      // Wait for challenge to appear
      await page.waitForSelector('#challenge-form, .cf-browser-verification, .cf-checking-browser',
        { timeout: 5000 }).catch(() => { });

      // Check if it's an interactive challenge
      const hasInteractiveChallenge = await page.evaluate(() => {
        return !!(
          document.querySelector('input[type="checkbox"]') ||
          document.querySelector('.cf-turnstile') ||
          document.querySelector('[data-cf-turnstile-sitekey]')
        );
      });

      if (hasInteractiveChallenge) {
        console.log('🧩 Interactive Cloudflare challenge detected');

        // Try to solve Turnstile if present
        const turnstileSiteKey = await page.evaluate(() => {
          const element = document.querySelector('[data-cf-turnstile-sitekey]');
          return element ? element.getAttribute('data-cf-turnstile-sitekey') : null;
        });

        if (turnstileSiteKey && this.solvers.twoCaptcha) {
          return await this.solveTurnstile(page, turnstileSiteKey);
        }

        // Try clicking checkbox if present
        const checkbox = await page.$('input[type="checkbox"]');
        if (checkbox) {
          await checkbox.click();
          await page.waitForTimeout(2000);
        }
      }

      // Wait for challenge to complete
      await page.waitForFunction(
        () => {
          const title = document.title;
          return !title.includes('Just a moment') &&
            !title.includes('Checking your browser') &&
            !title.includes('Chờ một chút') &&
            !document.querySelector('#challenge-form') &&
            !document.querySelector('.cf-browser-verification');
        },
        { timeout: 60000 }
      );

      console.log('✅ Cloudflare challenge completed');
      return true;
    } catch (error) {
      console.log('⚠️ Cloudflare challenge handling failed:', error.message);
      return false;
    }
  }

  /**
   * Solve reCAPTCHA v2
   */
  async solveRecaptchaV2(page, siteKey) {
    if (!this.solvers.twoCaptcha) {
      throw new Error('No CAPTCHA solver configured');
    }

    console.log('🧩 Solving reCAPTCHA v2...');

    const pageUrl = page.url();
    const solution = await this.solvers.twoCaptcha.solveRecaptchaV2(siteKey, pageUrl);

    // Inject solution
    await page.evaluate((token) => {
      document.getElementById('g-recaptcha-response').innerHTML = token;
      if (window.grecaptcha) {
        window.grecaptcha.getResponse = () => token;
      }
    }, solution);

    console.log('✅ reCAPTCHA v2 solved');
    return true;
  }

  /**
   * Solve Cloudflare Turnstile
   */
  async solveTurnstile(page, siteKey) {
    if (!this.solvers.twoCaptcha) {
      throw new Error('No CAPTCHA solver configured');
    }

    console.log('🧩 Solving Cloudflare Turnstile...');

    const pageUrl = page.url();
    const solution = await this.solvers.twoCaptcha.solveTurnstile(siteKey, pageUrl);

    // Inject solution
    await page.evaluate((token) => {
      const responseElement = document.querySelector('[name="cf-turnstile-response"]');
      if (responseElement) {
        responseElement.value = token;
      }
    }, solution);

    console.log('✅ Cloudflare Turnstile solved');
    return true;
  }

  /**
   * Handle any CAPTCHA automatically
   */
  async handleCaptcha(page) {
    const captchaInfo = await this.detectCaptcha(page);

    if (captchaInfo.detections.cloudflare.challenge) {
      return await this.handleCloudflareChallenge(page);
    }

    if (captchaInfo.detections.recaptcha.v2 && captchaInfo.siteKeys.recaptcha) {
      return await this.solveRecaptchaV2(page, captchaInfo.siteKeys.recaptcha);
    }

    if (captchaInfo.detections.cloudflare.turnstile && captchaInfo.siteKeys.cloudflare) {
      return await this.solveTurnstile(page, captchaInfo.siteKeys.cloudflare);
    }

    return false;
  }

  /**
   * Wait for manual CAPTCHA solving
   */
  async waitForManualSolving(page, timeout = 120000) {
    console.log('⏳ Waiting for manual CAPTCHA solving...');
    console.log('Please solve the CAPTCHA in the browser window');

    const startTime = Date.now();

    while (Date.now() - startTime < timeout) {
      const captchaInfo = await this.detectCaptcha(page);

      // Check if any CAPTCHA is still present
      const hasCaptcha = Object.values(captchaInfo.detections).some(detection => {
        if (typeof detection === 'object') {
          return Object.values(detection).some(Boolean);
        }
        return detection;
      });

      if (!hasCaptcha) {
        console.log('✅ CAPTCHA solved manually');
        return true;
      }

      await page.waitForTimeout(1000);
    }

    console.log('⏰ Manual CAPTCHA solving timeout');
    return false;
  }
}

module.exports = CaptchaHandler;
