/**
 * Session management for maintaining state across crawling sessions
 */

const fs = require('fs-extra');
const path = require('path');

class SessionManager {
  constructor(config) {
    this.config = config;
    this.sessionDir = path.join(process.cwd(), 'sessions');
    this.currentSession = null;
    this.sessionData = {
      cookies: [],
      localStorage: {},
      sessionStorage: {},
      userAgent: null,
      viewport: null,
      crawledUrls: new Set(),
      failedUrls: new Set(),
      lastActivity: null,
      totalProperties: 0
    };
  }

  /**
   * Initialize session management
   */
  async initialize() {
    await fs.ensureDir(this.sessionDir);
    console.log('📁 Session manager initialized');
  }

  /**
   * Create a new session
   */
  async createSession(sessionName = null) {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    this.currentSession = sessionName || `session_${timestamp}`;
    
    this.sessionData = {
      cookies: [],
      localStorage: {},
      sessionStorage: {},
      userAgent: null,
      viewport: null,
      crawledUrls: new Set(),
      failedUrls: new Set(),
      lastActivity: new Date().toISOString(),
      totalProperties: 0,
      startTime: new Date().toISOString(),
      sites: []
    };

    console.log(`🆕 Created new session: ${this.currentSession}`);
    return this.currentSession;
  }

  /**
   * Load existing session
   */
  async loadSession(sessionName) {
    try {
      const sessionPath = path.join(this.sessionDir, `${sessionName}.json`);
      
      if (await fs.pathExists(sessionPath)) {
        const data = await fs.readJson(sessionPath);
        
        this.currentSession = sessionName;
        this.sessionData = {
          ...data,
          crawledUrls: new Set(data.crawledUrls || []),
          failedUrls: new Set(data.failedUrls || [])
        };
        
        console.log(`📂 Loaded session: ${sessionName}`);
        console.log(`📊 Session stats: ${this.sessionData.crawledUrls.size} URLs crawled, ${this.sessionData.totalProperties} properties`);
        return true;
      }
    } catch (error) {
      console.error('❌ Failed to load session:', error);
    }
    
    return false;
  }

  /**
   * Save current session
   */
  async saveSession() {
    if (!this.currentSession) return;

    try {
      const sessionPath = path.join(this.sessionDir, `${this.currentSession}.json`);
      
      const dataToSave = {
        ...this.sessionData,
        crawledUrls: Array.from(this.sessionData.crawledUrls),
        failedUrls: Array.from(this.sessionData.failedUrls),
        lastActivity: new Date().toISOString()
      };

      await fs.writeJson(sessionPath, dataToSave, { spaces: 2 });
      console.log(`💾 Session saved: ${this.currentSession}`);
    } catch (error) {
      console.error('❌ Failed to save session:', error);
    }
  }

  /**
   * Apply session data to page
   */
  async applySessionToPage(page) {
    if (!this.sessionData) return;

    try {
      // Set cookies
      if (this.sessionData.cookies.length > 0) {
        await page.setCookie(...this.sessionData.cookies);
        console.log(`🍪 Applied ${this.sessionData.cookies.length} cookies`);
      }

      // Set user agent
      if (this.sessionData.userAgent) {
        await page.setUserAgent(this.sessionData.userAgent);
      }

      // Set viewport
      if (this.sessionData.viewport) {
        await page.setViewport(this.sessionData.viewport);
      }

      // Set localStorage and sessionStorage
      if (Object.keys(this.sessionData.localStorage).length > 0 || 
          Object.keys(this.sessionData.sessionStorage).length > 0) {
        
        await page.evaluateOnNewDocument((localStorage, sessionStorage) => {
          // Set localStorage
          Object.entries(localStorage).forEach(([key, value]) => {
            window.localStorage.setItem(key, value);
          });
          
          // Set sessionStorage
          Object.entries(sessionStorage).forEach(([key, value]) => {
            window.sessionStorage.setItem(key, value);
          });
        }, this.sessionData.localStorage, this.sessionData.sessionStorage);
      }

    } catch (error) {
      console.error('❌ Failed to apply session to page:', error);
    }
  }

  /**
   * Capture session data from page
   */
  async captureSessionFromPage(page) {
    try {
      // Get cookies
      this.sessionData.cookies = await page.cookies();

      // Get user agent
      this.sessionData.userAgent = await page.evaluate(() => navigator.userAgent);

      // Get viewport
      this.sessionData.viewport = page.viewport();

      // Get localStorage and sessionStorage
      const storageData = await page.evaluate(() => {
        const localStorage = {};
        const sessionStorage = {};

        // Capture localStorage
        for (let i = 0; i < window.localStorage.length; i++) {
          const key = window.localStorage.key(i);
          localStorage[key] = window.localStorage.getItem(key);
        }

        // Capture sessionStorage
        for (let i = 0; i < window.sessionStorage.length; i++) {
          const key = window.sessionStorage.key(i);
          sessionStorage[key] = window.sessionStorage.getItem(key);
        }

        return { localStorage, sessionStorage };
      });

      this.sessionData.localStorage = storageData.localStorage;
      this.sessionData.sessionStorage = storageData.sessionStorage;

      console.log('📸 Session data captured from page');
    } catch (error) {
      console.error('❌ Failed to capture session data:', error);
    }
  }

  /**
   * Mark URL as crawled
   */
  markUrlCrawled(url, propertyCount = 0) {
    this.sessionData.crawledUrls.add(url);
    this.sessionData.totalProperties += propertyCount;
    this.sessionData.lastActivity = new Date().toISOString();
  }

  /**
   * Mark URL as failed
   */
  markUrlFailed(url, error = null) {
    this.sessionData.failedUrls.add(url);
    this.sessionData.lastActivity = new Date().toISOString();
    
    if (error) {
      console.log(`❌ URL failed: ${url} - ${error}`);
    }
  }

  /**
   * Check if URL was already crawled
   */
  isUrlCrawled(url) {
    return this.sessionData.crawledUrls.has(url);
  }

  /**
   * Check if URL previously failed
   */
  isUrlFailed(url) {
    return this.sessionData.failedUrls.has(url);
  }

  /**
   * Get session statistics
   */
  getSessionStats() {
    return {
      sessionName: this.currentSession,
      startTime: this.sessionData.startTime,
      lastActivity: this.sessionData.lastActivity,
      crawledUrls: this.sessionData.crawledUrls.size,
      failedUrls: this.sessionData.failedUrls.size,
      totalProperties: this.sessionData.totalProperties,
      sites: this.sessionData.sites.length
    };
  }

  /**
   * List all available sessions
   */
  async listSessions() {
    try {
      const files = await fs.readdir(this.sessionDir);
      const sessions = files
        .filter(file => file.endsWith('.json'))
        .map(file => file.replace('.json', ''));
      
      return sessions;
    } catch (error) {
      console.error('❌ Failed to list sessions:', error);
      return [];
    }
  }

  /**
   * Delete a session
   */
  async deleteSession(sessionName) {
    try {
      const sessionPath = path.join(this.sessionDir, `${sessionName}.json`);
      await fs.remove(sessionPath);
      console.log(`🗑️ Deleted session: ${sessionName}`);
      return true;
    } catch (error) {
      console.error('❌ Failed to delete session:', error);
      return false;
    }
  }

  /**
   * Auto-save session periodically
   */
  startAutoSave(intervalMs = 60000) {
    if (this.autoSaveInterval) {
      clearInterval(this.autoSaveInterval);
    }

    this.autoSaveInterval = setInterval(async () => {
      await this.saveSession();
    }, intervalMs);

    console.log(`⏰ Auto-save enabled (every ${intervalMs / 1000}s)`);
  }

  /**
   * Stop auto-save
   */
  stopAutoSave() {
    if (this.autoSaveInterval) {
      clearInterval(this.autoSaveInterval);
      this.autoSaveInterval = null;
      console.log('⏹️ Auto-save stopped');
    }
  }

  /**
   * Cleanup on exit
   */
  async cleanup() {
    this.stopAutoSave();
    await this.saveSession();
  }
}

module.exports = SessionManager;
