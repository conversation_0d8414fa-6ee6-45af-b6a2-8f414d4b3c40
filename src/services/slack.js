/**
 * Slack notification service for rental property updates
 */

const axios = require('axios');
require('dotenv').config();

class SlackService {
  constructor() {
    this.webhookUrl = process.env.SLACK_WEBHOOK_URL;
    if (!this.webhookUrl) {
      throw new Error('SLACK_WEBHOOK_URL environment variable is required');
    }
  }

  /**
   * Format property data for Slack message
   */
  formatProperty(property) {
    const cleanPrice = property.price ? property.price.replace(/[^\d,]/g, '') : 'N/A';
    const cleanLocation = property.location ? property.location.split('•')[0].trim() : 'N/A';
    
    // Create property block
    const propertyBlock = {
      type: "section",
      text: {
        type: "mrkdwn",
        text: `*${property.title}*\n💰 *${cleanPrice} triệu/tháng*\n📍 ${cleanLocation}\n🕒 ${new Date(property.extractedAt).toLocaleString('vi-VN')}`
      }
    };

    // Add image if available
    if (property.images && property.images.length > 0) {
      const validImage = property.images.find(img => 
        img && 
        img.startsWith('http') && 
        !img.includes('data:image/gif;base64')
      );
      
      if (validImage) {
        propertyBlock.accessory = {
          type: "image",
          image_url: validImage,
          alt_text: property.title
        };
      }
    }

    // Add action button
    const actionBlock = {
      type: "actions",
      elements: [
        {
          type: "button",
          text: {
            type: "plain_text",
            text: "Xem chi tiết"
          },
          url: property.url,
          style: "primary"
        }
      ]
    };

    return [propertyBlock, actionBlock, { type: "divider" }];
  }

  /**
   * Send initial crawl results to Slack
   */
  async sendInitialResults(properties) {
    try {
      const totalProperties = properties.length;
      const propertiesWithImages = properties.filter(p => 
        p.images && p.images.length > 0 && 
        p.images.some(img => img.startsWith('http') && !img.includes('data:image/gif;base64'))
      ).length;

      // Header message
      const headerBlocks = [
        {
          type: "header",
          text: {
            type: "plain_text",
            text: "🏠 Rental Property Crawler - Initial Results"
          }
        },
        {
          type: "section",
          text: {
            type: "mrkdwn",
            text: `📊 *Summary:*\n• Total properties found: *${totalProperties}*\n• Properties with images: *${propertiesWithImages}*\n• Crawled at: ${new Date().toLocaleString('vi-VN')}`
          }
        },
        {
          type: "divider"
        }
      ];

      // Limit to first 10 properties to avoid message size limits
      const propertiesToShow = properties.slice(0, 10);
      let blocks = [...headerBlocks];

      propertiesToShow.forEach(property => {
        blocks.push(...this.formatProperty(property));
      });

      // Add footer if there are more properties
      if (totalProperties > 10) {
        blocks.push({
          type: "context",
          elements: [
            {
              type: "mrkdwn",
              text: `_Showing first 10 of ${totalProperties} properties. More properties available in the full report._`
            }
          ]
        });
      }

      const message = {
        blocks: blocks
      };

      await axios.post(this.webhookUrl, message);
      console.log(`✅ Sent initial results to Slack: ${totalProperties} properties`);
      
    } catch (error) {
      console.error('❌ Failed to send initial results to Slack:', error.message);
      throw error;
    }
  }

  /**
   * Send new properties to Slack
   */
  async sendNewProperties(newProperties) {
    try {
      if (newProperties.length === 0) {
        // Send "no new properties" message
        const message = {
          blocks: [
            {
              type: "header",
              text: {
                type: "plain_text",
                text: "🔄 Rental Property Update"
              }
            },
            {
              type: "section",
              text: {
                type: "mrkdwn",
                text: `✅ *No new properties found*\nLast checked: ${new Date().toLocaleString('vi-VN')}`
              }
            }
          ]
        };
        
        await axios.post(this.webhookUrl, message);
        console.log('✅ Sent "no new properties" update to Slack');
        return;
      }

      const propertiesWithImages = newProperties.filter(p => 
        p.images && p.images.length > 0 && 
        p.images.some(img => img.startsWith('http') && !img.includes('data:image/gif;base64'))
      ).length;

      // Header message
      const headerBlocks = [
        {
          type: "header",
          text: {
            type: "plain_text",
            text: "🆕 New Rental Properties Found!"
          }
        },
        {
          type: "section",
          text: {
            type: "mrkdwn",
            text: `📊 *New Properties:*\n• Total new: *${newProperties.length}*\n• With images: *${propertiesWithImages}*\n• Found at: ${new Date().toLocaleString('vi-VN')}`
          }
        },
        {
          type: "divider"
        }
      ];

      // Limit to first 10 new properties
      const propertiesToShow = newProperties.slice(0, 10);
      let blocks = [...headerBlocks];

      propertiesToShow.forEach(property => {
        blocks.push(...this.formatProperty(property));
      });

      // Add footer if there are more properties
      if (newProperties.length > 10) {
        blocks.push({
          type: "context",
          elements: [
            {
              type: "mrkdwn",
              text: `_Showing first 10 of ${newProperties.length} new properties._`
            }
          ]
        });
      }

      const message = {
        blocks: blocks
      };

      await axios.post(this.webhookUrl, message);
      console.log(`✅ Sent ${newProperties.length} new properties to Slack`);
      
    } catch (error) {
      console.error('❌ Failed to send new properties to Slack:', error.message);
      throw error;
    }
  }

  /**
   * Send error notification to Slack
   */
  async sendError(error, context = '') {
    try {
      const message = {
        blocks: [
          {
            type: "header",
            text: {
              type: "plain_text",
              text: "❌ Crawler Error"
            }
          },
          {
            type: "section",
            text: {
              type: "mrkdwn",
              text: `*Error occurred:* ${context}\n\`\`\`${error.message}\`\`\`\n*Time:* ${new Date().toLocaleString('vi-VN')}`
            }
          }
        ]
      };

      await axios.post(this.webhookUrl, message);
      console.log('✅ Sent error notification to Slack');
      
    } catch (slackError) {
      console.error('❌ Failed to send error to Slack:', slackError.message);
    }
  }
}

module.exports = SlackService;
