/**
 * <PERSON><PERSON>t (nhatot.com) specific data extractor
 */

class ChototExtractor {
  constructor(config) {
    this.config = config.sites.chotot;
    this.selectors = this.config.selectors;
  }

  async extractPropertyData(page) {
    try {
      console.log("🔍 Extracting property data from Chotot...");

      // Try multiple selectors to find property cards
      const selectorList = this.selectors.propertyCard.split(", ");
      let foundSelector = null;

      for (const selector of selectorList) {
        try {
          await page.waitForSelector(selector.trim(), { timeout: 5000 });
          foundSelector = selector.trim();
          console.log(`✅ Found elements with selector: ${foundSelector}`);
          break;
        } catch (error) {
          console.log(
            `⚠️ Selector ${selector.trim()} not found, trying next...`
          );
        }
      }

      if (!foundSelector) {
        console.log("❌ No property cards found with any selector");
        return [];
      }

      // Extract all property data using the found selector
      const properties = await page.evaluate(
        (selectors, foundSelector) => {
          const propertyCards = document.querySelectorAll(foundSelector);
          const results = [];

          propertyCards.forEach((card, index) => {
            try {
              const property = {
                id: `chotot_${Date.now()}_${index}`,
                source: "nhatot.com",
                extractedAt: new Date().toISOString(),
              };

              // Helper function to try multiple selectors
              const trySelectors = (selectorString) => {
                const selectors = selectorString.split(", ");
                for (const selector of selectors) {
                  const element = card.querySelector(selector.trim());
                  if (element) return element;
                }
                return null;
              };

              // Extract title
              const titleElement = trySelectors(selectors.title);
              property.title = titleElement
                ? titleElement.textContent.trim()
                : "";

              // Extract price
              const priceElement = trySelectors(selectors.price);
              property.price = priceElement
                ? priceElement.textContent.trim()
                : "";

              // Extract location
              const locationElement = trySelectors(selectors.location);
              property.location = locationElement
                ? locationElement.textContent.trim()
                : "";

              // Extract area
              const areaElement = trySelectors(selectors.area);
              property.area = areaElement ? areaElement.textContent.trim() : "";

              // Extract description
              const descElement = trySelectors(selectors.description);
              property.description = descElement
                ? descElement.textContent.trim()
                : "";

              // Extract images with enhanced filtering
              const imageSelectors = selectors.images.split(", ");
              property.images = [];

              for (const imgSelector of imageSelectors) {
                const imageElements = card.querySelectorAll(imgSelector.trim());
                if (imageElements.length > 0) {
                  const validImages = Array.from(imageElements)
                    .map((img) => {
                      // Get image URL from src or data-src
                      const imageUrl =
                        img.src ||
                        img.dataset.src ||
                        img.getAttribute("data-src");
                      return {
                        url: imageUrl,
                        width: img.width || img.naturalWidth || 0,
                        height: img.height || img.naturalHeight || 0,
                        alt: img.alt || "",
                        element: img,
                      };
                    })
                    .filter((imgData) => {
                      // Filter out invalid or unwanted images
                      if (!imgData.url) return false;

                      // Exclude placeholder/lazy loading images
                      if (
                        imgData.url.includes("data:image/gif;base64") ||
                        imgData.url.includes("placeholder") ||
                        imgData.url.includes("lazy")
                      )
                        return false;

                      // Exclude logos, icons, and profile images
                      if (
                        imgData.url.includes("logo") ||
                        imgData.url.includes("icon") ||
                        imgData.url.includes("avatar") ||
                        imgData.url.includes("profile") ||
                        imgData.url.includes("uac2/")
                      )
                        return false;

                      // Exclude very small images (likely icons)
                      if (
                        imgData.width > 0 &&
                        imgData.height > 0 &&
                        (imgData.width < 80 || imgData.height < 80)
                      )
                        return false;

                      // Only accept real image URLs (prefer CDN images from chotot)
                      return (
                        imgData.url.startsWith("http") &&
                        (imgData.url.includes("cdn.chotot.com") ||
                          imgData.url.includes(".jpg") ||
                          imgData.url.includes(".jpeg") ||
                          imgData.url.includes(".png") ||
                          imgData.url.includes(".webp"))
                      );
                    })
                    .sort((a, b) => {
                      // Prioritize larger images and CDN images
                      const aScore =
                        a.width * a.height +
                        (a.url.includes("cdn.chotot.com") ? 10000 : 0);
                      const bScore =
                        b.width * b.height +
                        (b.url.includes("cdn.chotot.com") ? 10000 : 0);
                      return bScore - aScore;
                    })
                    .map((imgData) => imgData.url)
                    .slice(0, 5); // Limit to 5 images per property

                  if (validImages.length > 0) {
                    property.images = validImages;
                    break;
                  }
                }
              }

              // If no images found with selectors, try to find any property images in the card
              if (property.images.length === 0) {
                const allCardImages = card.querySelectorAll("img");
                const fallbackImages = Array.from(allCardImages)
                  .map((img) => img.src || img.dataset.src)
                  .filter(
                    (url) =>
                      url &&
                      url.includes("cdn.chotot.com") &&
                      !url.includes("logo") &&
                      !url.includes("icon") &&
                      !url.includes("uac2/")
                  )
                  .slice(0, 3);

                property.images = fallbackImages;
              }

              // Extract property URL
              const linkElement = card.querySelector("a[href]");
              property.url = linkElement ? linkElement.href : "";

              // Extract additional attributes
              const attributes = {};
              const attrElements = card.querySelectorAll(
                '[data-testid*="attr"]'
              );
              attrElements.forEach((attr) => {
                const key = attr.getAttribute("data-testid");
                const value = attr.textContent.trim();
                if (key && value) {
                  attributes[key] = value;
                }
              });
              property.attributes = attributes;

              // Only add if we have essential data
              if (property.title && (property.price || property.location)) {
                results.push(property);
              }
            } catch (error) {
              console.error("Error extracting property:", error);
            }
          });

          return results;
        },
        this.selectors,
        foundSelector
      );

      console.log(`✅ Extracted ${properties.length} properties`);
      return properties;
    } catch (error) {
      console.error("❌ Error extracting property data:", error);
      return [];
    }
  }

  async hasNextPage(page) {
    try {
      const nextButton = await page.$(this.selectors.nextPage);
      const loadMoreButton = await page.$(this.selectors.loadMoreButton);

      return !!(nextButton || loadMoreButton);
    } catch (error) {
      return false;
    }
  }

  async goToNextPage(page) {
    try {
      // Try pagination next button first
      const nextButton = await page.$(this.selectors.nextPage);
      if (nextButton) {
        const isDisabled = await page.evaluate(
          (el) =>
            el.disabled ||
            el.classList.contains("disabled") ||
            el.getAttribute("aria-disabled") === "true",
          nextButton
        );

        if (!isDisabled) {
          await nextButton.click();
          await page.waitForNavigation({ waitUntil: "networkidle2" });
          return true;
        }
      }

      // Try load more button
      const loadMoreButton = await page.$(this.selectors.loadMoreButton);
      if (loadMoreButton) {
        await loadMoreButton.click();
        await new Promise((resolve) => setTimeout(resolve, 3000)); // Wait for content to load
        return true;
      }

      return false;
    } catch (error) {
      console.error("Error navigating to next page:", error);
      return false;
    }
  }

  async scrollToLoadMore(page, maxScrolls = 5) {
    try {
      let scrollCount = 0;
      let previousHeight = 0;

      while (scrollCount < maxScrolls) {
        // Get current page height
        const currentHeight = await page.evaluate(
          () => document.body.scrollHeight
        );

        if (currentHeight === previousHeight) {
          break; // No more content to load
        }

        // Scroll to bottom
        await page.evaluate(() => {
          window.scrollTo(0, document.body.scrollHeight);
        });

        // Wait for potential new content
        await new Promise((resolve) => setTimeout(resolve, 2000));

        previousHeight = currentHeight;
        scrollCount++;

        console.log(`📜 Scroll ${scrollCount}/${maxScrolls} completed`);
      }

      // Scroll back to top
      await page.evaluate(() => window.scrollTo(0, 0));
      await new Promise((resolve) => setTimeout(resolve, 1000));

      return scrollCount;
    } catch (error) {
      console.error("Error during scroll loading:", error);
      return 0;
    }
  }

  cleanPropertyData(property) {
    // Clean and normalize property data
    const cleaned = { ...property };

    // Clean price
    if (cleaned.price) {
      cleaned.price = cleaned.price.replace(/[^\d.,]/g, "").trim();
    }

    // Clean area
    if (cleaned.area) {
      cleaned.area = cleaned.area.replace(/[^\d.,m²]/g, "").trim();
    }

    // Clean location
    if (cleaned.location) {
      cleaned.location = cleaned.location.replace(/\s+/g, " ").trim();
    }

    // Clean title
    if (cleaned.title) {
      cleaned.title = cleaned.title.replace(/\s+/g, " ").trim();
    }

    return cleaned;
  }
}

module.exports = ChototExtractor;
