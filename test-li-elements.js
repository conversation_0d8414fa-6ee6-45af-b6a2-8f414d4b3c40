/**
 * Test script to examine the LI elements within ListAds_ListAds__ANK2d
 */

const StealthBrowser = require('./src/utils/stealth');
const config = require('./src/config');

async function testLiElements() {
  const browser = new StealthBrowser(config);
  
  try {
    console.log('🚀 Launching browser...');
    const page = await browser.launch();
    
    console.log('🌐 Navigating to Chotot...');
    await browser.navigateWithRetry('https://www.nhatot.com/thue-bat-dong-san-quan-7-tp-ho-chi-minh?price=5000000-8000000');
    
    console.log('⏳ Waiting for page to fully load...');
    await new Promise(resolve => setTimeout(resolve, 10000));
    
    // Test the LI elements within ListAds
    console.log('\n🔍 Testing LI elements within ListAds_ListAds__ANK2d...');
    
    const liTest = await page.evaluate(() => {
      const results = {
        containerFound: false,
        ulElements: [],
        liElements: [],
        sampleProperties: []
      };
      
      // Check for the main container
      const containers = document.querySelectorAll('.ListAds_ListAds__ANK2d');
      results.containerFound = containers.length > 0;
      
      if (containers.length > 0) {
        const container = containers[0];
        
        // Find UL elements
        const ulElements = container.querySelectorAll('ul');
        results.ulElements = Array.from(ulElements).map(ul => ({
          className: ul.className,
          childrenCount: ul.children.length,
          tagName: ul.tagName
        }));
        
        // Find LI elements
        const liElements = container.querySelectorAll('li');
        results.liElements = Array.from(liElements).slice(0, 10).map((li, index) => ({
          index,
          className: li.className,
          textPreview: li.textContent.substring(0, 300).trim(),
          hasLinks: li.querySelectorAll('a').length,
          hasImages: li.querySelectorAll('img').length,
          hasH3: li.querySelectorAll('h3').length,
          innerHTML: li.innerHTML.substring(0, 500)
        }));
        
        // Try to extract property data from first few LI elements
        results.sampleProperties = Array.from(liElements).slice(0, 3).map((li, index) => {
          const property = {
            index,
            title: '',
            price: '',
            location: '',
            images: []
          };
          
          // Try to find title
          const h3 = li.querySelector('h3');
          if (h3) property.title = h3.textContent.trim();
          
          // Try to find price (look for patterns)
          const text = li.textContent;
          const priceMatch = text.match(/(\d+[.,]\d+[.,]\d+|\d+\s*triệu|\d+\s*tr)/);
          if (priceMatch) property.price = priceMatch[0];
          
          // Try to find location (often after price)
          const locationMatch = text.match(/(Phường|Quận|Huyện|Thành phố)[\s\w]+/);
          if (locationMatch) property.location = locationMatch[0];
          
          // Count images
          property.images = li.querySelectorAll('img').length;
          
          return property;
        });
      }
      
      return results;
    });
    
    console.log('\n📊 LI Elements Test Results:');
    console.log(`Container found: ${liTest.containerFound}`);
    
    if (liTest.containerFound) {
      console.log('\n📋 UL Elements:');
      liTest.ulElements.forEach((ul, index) => {
        console.log(`UL ${index + 1}: ${ul.tagName}.${ul.className} (${ul.childrenCount} children)`);
      });
      
      console.log(`\n📋 Found ${liTest.liElements.length} LI elements:`);
      liTest.liElements.forEach(li => {
        console.log(`\nLI ${li.index + 1}:`);
        console.log(`  Class: ${li.className}`);
        console.log(`  Links: ${li.hasLinks}, Images: ${li.hasImages}, H3: ${li.hasH3}`);
        console.log(`  Text: ${li.textPreview}`);
        console.log(`  HTML preview: ${li.innerHTML.substring(0, 200)}...`);
      });
      
      console.log('\n🏠 Sample Property Extraction:');
      liTest.sampleProperties.forEach(prop => {
        console.log(`\nProperty ${prop.index + 1}:`);
        console.log(`  Title: ${prop.title}`);
        console.log(`  Price: ${prop.price}`);
        console.log(`  Location: ${prop.location}`);
        console.log(`  Images: ${prop.images}`);
      });
    }
    
    // Test the updated selector
    console.log('\n🔍 Testing updated property card selector...');
    
    const selectorTest = await page.evaluate(() => {
      const selectors = [
        '.ListAds_ListAds__ANK2d li',
        '.ListAds_ListAds__ANK2d > ul > li'
      ];
      
      const results = {};
      
      selectors.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        results[selector] = {
          count: elements.length,
          hasContent: elements.length > 0 && elements[0].textContent.trim().length > 50
        };
      });
      
      return results;
    });
    
    console.log('\n📊 Updated Selector Test Results:');
    Object.entries(selectorTest).forEach(([selector, result]) => {
      console.log(`${selector}: ${result.count} elements found, has content: ${result.hasContent}`);
    });
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await browser.close();
  }
}

testLiElements();
