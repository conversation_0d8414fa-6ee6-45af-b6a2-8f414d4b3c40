#!/usr/bin/env node

/**
 * Entry point for the Rental Property Crawler
 */

const chalk = require('chalk');
const RentalCrawler = require('./src/crawler');

async function main() {
  console.log(chalk.blue.bold('\n🏠 Rental Property Crawler'));
  console.log(chalk.gray('Crawling rental properties from Cloudflare-protected sites\n'));

  const crawler = new RentalCrawler();
  
  try {
    await crawler.run();
    process.exit(0);
  } catch (error) {
    console.error(chalk.red('\n💥 Fatal error:'), error.message);
    console.error(chalk.gray(error.stack));
    process.exit(1);
  }
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log(chalk.yellow('\n⚠️ Received SIGINT, shutting down gracefully...'));
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log(chalk.yellow('\n⚠️ Received SIGTERM, shutting down gracefully...'));
  process.exit(0);
});

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error(chalk.red('💥 Unhandled Rejection at:'), promise);
  console.error(chalk.red('Reason:'), reason);
  process.exit(1);
});

if (require.main === module) {
  main();
}

module.exports = main;
