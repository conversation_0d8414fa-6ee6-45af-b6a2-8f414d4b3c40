# Chotot (Nhatot.com) Adaptation Guide

This guide explains how to adapt the rental property crawler to work specifically with <PERSON><PERSON><PERSON> (nhatot.com), which has strong Cloudflare protection.

## Current Status

The application framework is fully functional and has been tested with a demo site. However, Chotot has very strong Cloudflare protection that requires additional techniques to bypass.

## Cloudflare Challenge Analysis

When testing with <PERSON>tot, we encountered:
- **Challenge Page**: "Chờ một chút..." (Wait a moment...)
- **Protection Level**: High - requires browser verification
- **Challenge Type**: JavaScript challenge with browser fingerprinting

## Recommended Solutions

### 1. Use Residential Proxies

The most effective approach is to use residential proxy services:

```javascript
// Add to src/config.js browser settings
browser: {
  headless: true,
  args: [
    '--proxy-server=http://your-proxy-server:port',
    // ... other args
  ]
}
```

**Recommended Proxy Services:**
- Bright Data (formerly Luminati)
- Oxylabs
- Smartproxy
- ProxyMesh

### 2. Enhanced Stealth Configuration

Update `src/utils/stealth.js` with more advanced techniques:

```javascript
// Add to launch() method
await this.page.evaluateOnNewDocument(() => {
  // More comprehensive webdriver removal
  delete navigator.__proto__.webdriver;
  
  // Override chrome runtime
  window.chrome = {
    runtime: {}
  };
  
  // Mock plugins
  Object.defineProperty(navigator, 'plugins', {
    get: () => [
      {
        0: {type: "application/x-google-chrome-pdf", suffixes: "pdf", description: "Portable Document Format", enabledPlugin: Plugin},
        description: "Portable Document Format",
        filename: "internal-pdf-viewer",
        length: 1,
        name: "Chrome PDF Plugin"
      }
    ]
  });
});
```

### 3. Correct CSS Selectors for Chotot

Based on manual inspection, update `src/config.js` with actual Chotot selectors:

```javascript
sites: {
  chotot: {
    baseUrl: 'https://www.nhatot.com',
    selectors: {
      // These need to be discovered by manual inspection
      propertyCard: '.AdItem_adItem__[hash], .ad-item, [data-ad-id]',
      title: '.AdItem_adTitle__[hash], .ad-title, h3 a',
      price: '.AdItem_adPrice__[hash], .ad-price, .price',
      location: '.AdItem_adLocation__[hash], .ad-location, .location',
      area: '.AdItem_adArea__[hash], .ad-area, .area',
      description: '.AdItem_adDescription__[hash], .ad-description',
      images: '.AdItem_adImage__[hash] img, .ad-image img',
      nextPage: '.Pagination_next__[hash], .pagination .next',
      loadMoreButton: '.LoadMore_button__[hash], .load-more'
    },
    waitForSelector: '.AdItem_adItem__[hash], .ad-item',
    maxScrolls: 3
  }
}
```

### 4. Manual Selector Discovery

To find the correct selectors:

1. **Disable Cloudflare temporarily** (if possible) or use a VPN
2. **Use browser developer tools** to inspect the page structure
3. **Run the selector discovery script**:

```bash
# Set headless: false in config first
node discover-selectors.js
```

4. **Update selectors** in `src/config.js` based on findings

### 5. Alternative Approaches

#### A. API Approach
Check if Chotot has a public API:
- Look for mobile app API endpoints
- Check for RSS feeds or sitemaps
- Use network tab to find AJAX endpoints

#### B. Selenium with Undetected ChromeDriver
Consider using Python with undetected-chromedriver:

```python
from undetected_chromedriver import Chrome
import time

driver = Chrome()
driver.get("https://www.nhatot.com/thue-bat-dong-san")
time.sleep(10)  # Wait for challenge
```

#### C. Browser Extension Approach
Create a browser extension that runs in a real browser session.

### 6. Rate Limiting and Ethics

When successfully bypassing Cloudflare:

```javascript
// Increase delays in src/config.js
crawling: {
  delay: {
    min: 10000,  // 10 seconds minimum
    max: 20000   // 20 seconds maximum
  },
  retries: 2,
  timeout: 60000,
  maxPages: 5    // Limit pages per session
}
```

## Implementation Steps

### Step 1: Get Proxy Service
1. Sign up for a residential proxy service
2. Get proxy credentials and endpoints
3. Test proxy with a simple request

### Step 2: Update Configuration
```javascript
// src/config.js
module.exports = {
  proxy: {
    enabled: true,
    host: 'your-proxy-host',
    port: 'your-proxy-port',
    username: 'your-username',
    password: 'your-password'
  },
  // ... rest of config
};
```

### Step 3: Modify Stealth Browser
```javascript
// src/utils/stealth.js
async launch() {
  const proxyArgs = this.config.proxy.enabled ? [
    `--proxy-server=http://${this.config.proxy.host}:${this.config.proxy.port}`
  ] : [];

  this.browser = await puppeteer.launch({
    headless: this.config.browser.headless,
    args: [...this.config.browser.args, ...proxyArgs]
  });
  
  // Set proxy authentication if needed
  if (this.config.proxy.enabled && this.config.proxy.username) {
    await this.page.authenticate({
      username: this.config.proxy.username,
      password: this.config.proxy.password
    });
  }
}
```

### Step 4: Test and Iterate
1. Start with simple pages
2. Gradually increase complexity
3. Monitor for detection
4. Adjust delays and behavior

## Legal and Ethical Considerations

⚠️ **Important**: Before implementing these solutions:

1. **Check Terms of Service**: Review Chotot's ToS regarding automated access
2. **Respect robots.txt**: Check https://www.nhatot.com/robots.txt
3. **Rate Limiting**: Use conservative delays to avoid overloading servers
4. **Data Usage**: Ensure compliance with Vietnamese data protection laws
5. **Commercial Use**: Consider reaching out to Chotot for API access if for commercial purposes

## Alternative: Contact Chotot

For legitimate business use cases, consider:
1. Contacting Chotot directly for API access
2. Proposing a partnership or data sharing agreement
3. Using official RSS feeds or data exports if available

## Testing the Current Implementation

To test the current implementation:

```bash
# Test with demo site (works)
npm start

# Test with Chotot (will hit Cloudflare)
# Edit urls.json to only include Chotot, then:
npm start
```

The demo implementation shows that the crawler framework is solid and ready for adaptation once the Cloudflare challenge is resolved.
