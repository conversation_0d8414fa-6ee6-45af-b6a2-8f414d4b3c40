/**
 * Test script to verify the ListAds_ListAds__ANK2d selector works correctly
 */

const StealthBrowser = require('./src/utils/stealth');
const config = require('./src/config');

async function testListAdsSelector() {
  const browser = new StealthBrowser(config);
  
  try {
    console.log('🚀 Launching browser...');
    const page = await browser.launch();
    
    console.log('🌐 Navigating to Chotot...');
    await browser.navigateWithRetry('https://www.nhatot.com/thue-bat-dong-san-quan-7-tp-ho-chi-minh?price=5000000-8000000');
    
    console.log('⏳ Waiting for page to fully load...');
    await new Promise(resolve => setTimeout(resolve, 10000));
    
    // Test the specific ListAds selector
    console.log('\n🔍 Testing ListAds_ListAds__ANK2d selector...');
    
    const listAdsTest = await page.evaluate(() => {
      const results = {
        containerFound: false,
        containerCount: 0,
        childrenCount: 0,
        sampleContent: [],
        childElements: []
      };
      
      // Check for the main container
      const containers = document.querySelectorAll('.ListAds_ListAds__ANK2d');
      results.containerFound = containers.length > 0;
      results.containerCount = containers.length;
      
      if (containers.length > 0) {
        const container = containers[0];
        const children = container.children;
        results.childrenCount = children.length;
        
        // Get info about first few children
        for (let i = 0; i < Math.min(5, children.length); i++) {
          const child = children[i];
          results.childElements.push({
            tagName: child.tagName,
            className: child.className,
            textPreview: child.textContent.substring(0, 200).trim(),
            hasLinks: child.querySelectorAll('a').length > 0,
            hasImages: child.querySelectorAll('img').length > 0,
            hasPrice: /\d+[.,]\d+[.,]\d+|\d+\s*triệu|\d+\s*tr/.test(child.textContent)
          });
        }
        
        // Get some sample content
        results.sampleContent = Array.from(children).slice(0, 3).map(child => 
          child.textContent.substring(0, 100).trim()
        );
      }
      
      return results;
    });
    
    console.log('\n📊 ListAds Container Test Results:');
    console.log(`Container found: ${listAdsTest.containerFound}`);
    console.log(`Container count: ${listAdsTest.containerCount}`);
    console.log(`Children count: ${listAdsTest.childrenCount}`);
    
    if (listAdsTest.containerFound) {
      console.log('\n🏠 Sample child elements:');
      listAdsTest.childElements.forEach((child, index) => {
        console.log(`Child ${index + 1}:`);
        console.log(`  Tag: ${child.tagName}, Class: ${child.className}`);
        console.log(`  Has links: ${child.hasLinks}, Has images: ${child.hasImages}, Has price: ${child.hasPrice}`);
        console.log(`  Text preview: ${child.textPreview}`);
        console.log('---');
      });
    }
    
    // Test the updated property card selectors
    console.log('\n🔍 Testing updated property card selectors...');
    
    const propertyCardTest = await page.evaluate(() => {
      const selectors = [
        '.ListAds_ListAds__ANK2d > div',
        '.ListAds_ListAds__ANK2d [class*="AdItem"]',
        '.AdItem_adItem__gDDQT',
        '.AdItem_adItem__2O6dw'
      ];
      
      const results = {};
      
      selectors.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        results[selector] = {
          count: elements.length,
          sampleText: elements.length > 0 ? elements[0].textContent.substring(0, 150).trim() : ''
        };
      });
      
      return results;
    });
    
    console.log('\n📊 Property Card Selector Test Results:');
    Object.entries(propertyCardTest).forEach(([selector, result]) => {
      console.log(`${selector}: ${result.count} elements found`);
      if (result.count > 0) {
        console.log(`  Sample text: ${result.sampleText}`);
      }
    });
    
    // Test for common property data elements
    console.log('\n🔍 Testing property data extraction...');
    
    const dataExtractionTest = await page.evaluate(() => {
      const results = {
        titles: [],
        prices: [],
        locations: [],
        images: []
      };
      
      // Try to find titles
      const titleSelectors = ['h3', 'h2', 'a[href*="/ad/"]', '[class*="title"]', '[class*="Title"]'];
      titleSelectors.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        if (elements.length > 0) {
          results.titles.push({
            selector,
            count: elements.length,
            sample: elements[0].textContent.trim().substring(0, 100)
          });
        }
      });
      
      // Try to find prices
      const priceSelectors = ['[class*="price"]', '[class*="Price"]'];
      priceSelectors.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        if (elements.length > 0) {
          results.prices.push({
            selector,
            count: elements.length,
            sample: elements[0].textContent.trim().substring(0, 50)
          });
        }
      });
      
      // Try to find locations
      const locationSelectors = ['[class*="location"]', '[class*="Location"]', '[class*="address"]'];
      locationSelectors.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        if (elements.length > 0) {
          results.locations.push({
            selector,
            count: elements.length,
            sample: elements[0].textContent.trim().substring(0, 100)
          });
        }
      });
      
      // Count images
      const images = document.querySelectorAll('img');
      results.images.push({
        selector: 'img',
        count: images.length,
        sample: images.length > 0 ? images[0].src : ''
      });
      
      return results;
    });
    
    console.log('\n📊 Data Extraction Test Results:');
    console.log('Titles found:');
    dataExtractionTest.titles.forEach(item => {
      console.log(`  ${item.selector}: ${item.count} elements - "${item.sample}"`);
    });
    
    console.log('Prices found:');
    dataExtractionTest.prices.forEach(item => {
      console.log(`  ${item.selector}: ${item.count} elements - "${item.sample}"`);
    });
    
    console.log('Locations found:');
    dataExtractionTest.locations.forEach(item => {
      console.log(`  ${item.selector}: ${item.count} elements - "${item.sample}"`);
    });
    
    console.log('Images found:');
    dataExtractionTest.images.forEach(item => {
      console.log(`  ${item.selector}: ${item.count} elements`);
    });
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await browser.close();
  }
}

testListAdsSelector();
