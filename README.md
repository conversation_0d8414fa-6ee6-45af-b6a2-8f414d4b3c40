# Rental Property Crawler

A powerful JavaScript application for crawling rental property data from Cloudflare-protected websites like Chotot (nhatot.com).

## Features

- 🛡️ **Cloudflare Bypass**: Uses advanced stealth techniques to bypass Cloudflare protection
- 🤖 **Human-like Behavior**: Simulates real user interactions with random delays and scrolling
- 📊 **Data Extraction**: Extracts comprehensive property information including title, price, location, area, and images
- 🔄 **Pagination Support**: Automatically handles pagination and "load more" buttons
- 💾 **Multiple Output Formats**: Saves data in JSON and CSV formats
- ⚙️ **Configurable**: Easy to configure for different sites and extraction requirements
- 🎯 **Retry Logic**: Built-in retry mechanisms for robust crawling

## Installation

1. **Clone or download the project**
2. **Install dependencies**:
   ```bash
   npm install
   ```

## Configuration

### URLs Configuration
Edit `urls.json` to specify the sites you want to crawl:

```json
{
  "site": [
    {
      "name": "Demo",
      "url": "https://quotes.toscrape.com/"
    },
    {
      "name": "<PERSON><PERSON><PERSON>",
      "url": "https://www.nhatot.com/thue-bat-dong-san-quan-7-tp-ho-chi-minh?price=5000000-8000000"
    }
  ]
}
```

### Application Configuration
Modify `src/config.js` to adjust crawling behavior:

- **Browser settings**: Headless mode, viewport size, user agent
- **Crawling settings**: Delays, retries, timeouts, max pages
- **Output settings**: Directory, format, filename
- **Site-specific selectors**: CSS selectors for data extraction

### Proxy Configuration
Copy `proxy.config.example.js` to `proxy.config.js` and configure:

```javascript
module.exports = {
  enabled: true,
  host: 'your-proxy-host.com',
  port: 8080,
  username: 'your-username',
  password: 'your-password',
  type: 'http'
};
```

### CAPTCHA Configuration
Copy `captcha.config.example.js` to `captcha.config.js` and configure:

```javascript
module.exports = {
  twoCaptcha: {
    apiKey: 'your-2captcha-api-key'
  },
  manual: {
    enabled: true,
    timeout: 300000
  }
};
```

## Usage

### Quick Start
```bash
# Start crawling with default settings
npm start

# Test your configuration
npm run test-config

# View available sessions
npm run sessions
```

### Command Line Interface

The crawler includes a comprehensive CLI with the following commands:

```bash
# Start a new crawling session
npm start
npm run crawl

# Resume an existing session
npm run resume <session-name>

# List all sessions
npm run sessions

# Get session information
npm run session <session-name>

# Delete a session
npm run delete-session <session-name>

# Test configuration
npm run test-config
```

### Data Visualization

```bash
# View crawled data in browser
npm run view

# View specific data file
npm run view file rental_properties_2024-01-15.json

# Start local server for better file access
npm run serve
```

### Advanced Usage

```bash
# Crawl without proxy
npm run crawl -- --no-proxy

# Limit pages per site
npm run crawl -- --max-pages 5

# Run with visible browser (for debugging)
npm run crawl -- --no-headless

# Enable CAPTCHA solving
npm run crawl -- --enable-captcha
```

## Output

The crawler saves extracted data in the `output/` directory:

- **JSON format**: Complete data with metadata
- **CSV format**: Tabular data for spreadsheet analysis
- **HTML Viewer**: Interactive web interface for data visualization

### Data Viewer

The included HTML data viewer (`viewer.html`) provides an interactive interface to:

- **Load and visualize** crawled data files
- **Search and filter** properties by title, location, or source
- **Sort data** by date, price, title, or location
- **View statistics** including total properties, sources, and average prices
- **Export data** to different formats

To use the data viewer:
1. Run `npm run view` to open the viewer
2. Click "Choose File" and select a JSON file from the `output/` directory
3. Browse and analyze your crawled data interactively

### Sample Output Structure
```json
{
  "metadata": {
    "totalProperties": 150,
    "extractedAt": "2024-01-15T10:30:00.000Z",
    "sites": ["nhatot.com"]
  },
  "properties": [
    {
      "id": "chotot_1705312200000_0",
      "source": "nhatot.com",
      "title": "Cho thuê căn hộ 2PN, đầy đủ nội thất",
      "price": "7,000,000",
      "location": "Quận 7, TP Hồ Chí Minh",
      "area": "70m²",
      "description": "Căn hộ đẹp, view sông...",
      "images": ["https://..."],
      "url": "https://www.nhatot.com/...",
      "extractedAt": "2024-01-15T10:30:00.000Z"
    }
  ]
}
```

## How It Works

### Cloudflare Bypass
The application uses several techniques to bypass Cloudflare protection:

1. **Puppeteer Extra with Stealth Plugin**: Removes automation detection
2. **Human-like Headers**: Sets realistic browser headers and user agent
3. **Geolocation**: Sets location to Vietnam for local content
4. **Random Delays**: Mimics human browsing patterns
5. **Challenge Detection**: Automatically waits for Cloudflare challenges to complete

### Data Extraction Process
1. **Navigation**: Loads the target URL with stealth techniques
2. **Content Loading**: Scrolls and waits for dynamic content to load
3. **Data Extraction**: Uses CSS selectors to extract property information
4. **Pagination**: Automatically navigates through multiple pages
5. **Data Cleaning**: Normalizes and cleans extracted data
6. **Output**: Saves results in structured formats

## Troubleshooting

### Common Issues

**Browser Launch Fails**
- Ensure you have sufficient system resources
- Try setting `headless: false` in config for debugging
- Check if Chrome/Chromium is properly installed

**Cloudflare Challenges**
- The application automatically handles most challenges
- If persistent issues occur, try increasing delays in config
- Consider using residential proxies for better success rates

**No Data Extracted**
- Check if the website structure has changed
- Update CSS selectors in `src/config.js`
- Enable non-headless mode to visually debug

**Memory Issues**
- Reduce `maxPages` in configuration
- Increase system memory or use a more powerful machine
- Process sites one at a time

### Debug Mode
Set `headless: false` in `src/config.js` to see the browser in action:

```javascript
browser: {
  headless: false, // Set to false for debugging
  // ... other settings
}
```

## Extending the Crawler

### Adding New Sites
1. Create a new extractor in `src/extractors/`
2. Add site configuration to `src/config.js`
3. Update the `getExtractor` method in `src/crawler.js`

### Customizing Data Extraction
Modify the CSS selectors in the site configuration to match the target website's structure.

## Legal and Ethical Considerations

- **Respect robots.txt**: Check the website's robots.txt file
- **Rate Limiting**: Use appropriate delays to avoid overwhelming servers
- **Terms of Service**: Ensure compliance with website terms of service
- **Data Usage**: Use extracted data responsibly and legally
- **Personal Data**: Be mindful of privacy regulations when handling personal information

## Dependencies

- **puppeteer**: Headless Chrome automation
- **puppeteer-extra**: Enhanced Puppeteer with plugins
- **puppeteer-extra-plugin-stealth**: Stealth plugin for bot detection bypass
- **puppeteer-extra-plugin-adblocker**: Ad blocking for faster loading
- **fs-extra**: Enhanced file system operations
- **chalk**: Terminal styling for better output

## License

ISC License - See package.json for details.

## Support

For issues and questions:
1. Check the troubleshooting section above
2. Review the configuration settings
3. Enable debug mode to investigate issues
4. Consider the legal and ethical guidelines
